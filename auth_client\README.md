# 授权码生成客户端

这是一个用于生成软件授权码的独立客户端工具，支持多种授权参数设置和历史记录管理。

## 功能特点

- 生成加密的授权码
- 支持永久授权和临时授权
- 可控制允许访问的功能模块
- 可限制允许查询的省份和车牌前缀
- 可设置每月查询次数限制
- 授权历史记录管理
- 支持将授权码上传到有道云笔记

## 安装说明

1. 确保已安装Python 3.8+
2. 安装依赖：
   ```
   pip install -r requirements.txt
   ```

## 使用方法

运行以下命令启动授权码生成客户端：

```
python run.py
```

## 授权参数说明

- **机器码**：用户设备的唯一标识
- **授权期限**：永久授权或临时授权（天数）
- **功能授权**：允许访问的功能模块
- **省份授权**：允许查询的省份前缀，如"粤,桂,京"
- **车牌前缀**：允许查询的车牌字母前缀，如"A,B,C"
- **每月查询次数**：限制每月可查询的次数，0表示不限制
- **备注**：记录客户信息等

## 数据存储

授权历史记录保存在`auth_history.db`数据库文件中。 