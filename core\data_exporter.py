#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据导出模块
支持导出违章数据为Excel、CSV等格式
"""

import os
import csv
import json
import tempfile
from datetime import datetime

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("警告: openpyxl未安装，Excel导出功能将不可用")


class ViolationDataExporter:
    """违章数据导出器"""
    
    def __init__(self):
        """初始化导出器"""
        self.temp_dir = tempfile.gettempdir()
    
    def export_to_excel(self, violations_data, plate):
        """导出为Excel文件

        Args:
            violations_data: 违章数据字典
            plate: 车牌号或描述（如"汇总(3个车牌)"）

        Returns:
            str: 导出文件路径，失败返回None
        """
        if not OPENPYXL_AVAILABLE:
            return None
        
        try:
            violations = violations_data.get('violations', [])
            if not violations:
                return None
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"违章记录_{plate}"
            
            # 设置标题
            if "汇总" in plate:
                title = f"违章记录汇总详情 - {plate}"
            else:
                title = f"车牌 {plate} 违章记录详情"
            ws.merge_cells('A1:I1')
            ws['A1'] = title
            ws['A1'].font = Font(size=16, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')
            
            # 设置查询时间
            query_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ws.merge_cells('A2:I2')
            ws['A2'] = f"查询时间: {query_time}"
            ws['A2'].alignment = Alignment(horizontal='center')

            # 设置表头（添加处理时间列）
            headers = ['序号', '车牌号', '违法时间', '处理时间', '违法地点', '违法描述', '罚款金额', '记分', '处理状态', '备注']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=4, column=col, value=header)
                cell.font = Font(bold=True, color='FFFFFF')
                cell.fill = PatternFill(start_color='4CAF50', end_color='4CAF50', fill_type='solid')
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # 填充数据
            for idx, violation in enumerate(violations, 1):
                row = idx + 4
                
                # 处理状态 - 使用与界面一致的判断逻辑
                clbj = violation.get('clbj', '')
                jkbj = violation.get('jkbj', '')
                fkje = violation.get('fkje', '0')
                points = violation.get('_points', '0')

                # 获取详细状态
                status = self._get_overall_status(clbj, jkbj, fkje, points)
                
                # 格式化处理时间
                clsj = violation.get('clsj', '')
                process_time = clsj if clsj else "未处理"

                data = [
                    idx,
                    violation.get('hphm', ''),  # 车牌号
                    violation.get('wfsj', ''),
                    process_time,  # 处理时间
                    violation.get('wfdz', ''),
                    violation.get('wfms', ''),
                    f"¥{violation.get('fkje', '0')}",
                    f"{violation.get('_points', '0')}分",
                    status,
                    violation.get('bz', '')
                ]
                
                for col, value in enumerate(data, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                    if col == 1:  # 序号居中
                        cell.alignment = Alignment(horizontal='center')
            
            # 添加汇总信息
            summary_row = len(violations) + 6
            ws.merge_cells(f'A{summary_row}:J{summary_row}')

            # 根据是否为汇总数据显示不同的汇总信息
            if violations_data.get('plate_count'):
                summary_text = (f"汇总: 涉及车牌 {violations_data.get('plate_count', 0)} 个, "
                              f"总违章数 {violations_data.get('total_count', len(violations))} 条, "
                              f"总罚款 ¥{violations_data.get('total_fine', 0)}, "
                              f"总记分 {violations_data.get('total_points', 0)} 分")
            else:
                summary_text = (f"汇总: 总违章数 {violations_data.get('total_count', len(violations))} 条, "
                              f"总罚款 ¥{violations_data.get('total_fine', 0)}, "
                              f"总记分 {violations_data.get('total_points', 0)} 分")

            ws[f'A{summary_row}'] = summary_text
            ws[f'A{summary_row}'].font = Font(bold=True)
            ws[f'A{summary_row}'].fill = PatternFill(start_color='FFF3CD', end_color='FFF3CD', fill_type='solid')

            # 自动调整列宽
            for col in range(1, 11):  # 现在有10列
                column_letter = get_column_letter(col)
                max_length = 0
                for row in ws[column_letter]:
                    try:
                        if len(str(row.value)) > max_length:
                            max_length = len(str(row.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            filename = f"违章记录_{plate}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = os.path.join(self.temp_dir, filename)
            wb.save(file_path)
            
            return file_path
            
        except Exception as e:
            print(f"导出Excel失败: {e}")
            return None
    
    def export_to_csv(self, violations_data, plate):
        """导出为CSV文件
        
        Args:
            violations_data: 违章数据字典
            plate: 车牌号
            
        Returns:
            str: 导出文件路径，失败返回None
        """
        try:
            violations = violations_data.get('violations', [])
            if not violations:
                return None
            
            filename = f"违章记录_{plate}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            file_path = os.path.join(self.temp_dir, filename)
            
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题
                if "汇总" in plate:
                    writer.writerow([f"违章记录汇总详情 - {plate}"])
                else:
                    writer.writerow([f"车牌 {plate} 违章记录详情"])
                writer.writerow([f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
                writer.writerow([])  # 空行

                # 写入表头
                headers = ['序号', '车牌号', '违法时间', '处理时间', '违法地点', '违法描述', '罚款金额', '记分', '处理状态', '备注']
                writer.writerow(headers)
                
                # 写入数据
                for idx, violation in enumerate(violations, 1):
                    # 处理状态 - 使用与界面一致的判断逻辑
                    clbj = violation.get('clbj', '')
                    jkbj = violation.get('jkbj', '')
                    fkje = violation.get('fkje', '0')
                    points = violation.get('_points', '0')

                    # 获取详细状态
                    status = self._get_overall_status(clbj, jkbj, fkje, points)
                    
                    # 格式化处理时间
                    clsj = violation.get('clsj', '')
                    process_time = clsj if clsj else "未处理"

                    row = [
                        idx,
                        violation.get('hphm', ''),  # 车牌号
                        violation.get('wfsj', ''),
                        process_time,  # 处理时间
                        violation.get('wfdz', ''),
                        violation.get('wfms', ''),
                        f"¥{violation.get('fkje', '0')}",
                        f"{violation.get('_points', '0')}分",
                        status,
                        violation.get('bz', '')
                    ]
                    writer.writerow(row)
                
                # 写入汇总
                writer.writerow([])  # 空行
                if violations_data.get('plate_count'):
                    summary = [
                        "汇总信息",
                        f"涉及车牌: {violations_data.get('plate_count', 0)}个",
                        f"总违章数: {violations_data.get('total_count', len(violations))}",
                        f"总罚款: ¥{violations_data.get('total_fine', 0)}",
                        f"总记分: {violations_data.get('total_points', 0)}分"
                    ]
                else:
                    summary = [
                        "汇总信息",
                        f"总违章数: {violations_data.get('total_count', len(violations))}",
                        f"总罚款: ¥{violations_data.get('total_fine', 0)}",
                        f"总记分: {violations_data.get('total_points', 0)}分"
                    ]
                writer.writerow(summary)
            
            return file_path
            
        except Exception as e:
            print(f"导出CSV失败: {e}")
            return None
    
    def export_to_json(self, violations_data, plate):
        """导出为JSON文件
        
        Args:
            violations_data: 违章数据字典
            plate: 车牌号
            
        Returns:
            str: 导出文件路径，失败返回None
        """
        try:
            violations = violations_data.get('violations', [])
            if not violations:
                return None
            
            # 构建导出数据
            export_data = {
                "车牌号": plate,
                "查询时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "汇总信息": {
                    "总违章数": violations_data.get('total_count', len(violations)),
                    "总罚款金额": violations_data.get('total_fine', 0),
                    "总记分": violations_data.get('total_points', 0)
                },
                "违章记录": []
            }
            
            # 处理违章记录
            for idx, violation in enumerate(violations, 1):
                # 处理状态 - 使用与界面一致的判断逻辑
                clbj = violation.get('clbj', '')
                jkbj = violation.get('jkbj', '')
                fkje = violation.get('fkje', '0')
                points = violation.get('_points', '0')

                # 获取详细状态
                status = self._get_overall_status(clbj, jkbj, fkje, points)
                
                # 格式化处理时间
                clsj = violation.get('clsj', '')
                process_time = clsj if clsj else "未处理"

                record = {
                    "序号": idx,
                    "违法时间": violation.get('wfsj', ''),
                    "处理时间": process_time,
                    "违法地点": violation.get('wfdz', ''),
                    "违法描述": violation.get('wfms', ''),
                    "罚款金额": violation.get('fkje', '0'),
                    "记分": violation.get('_points', '0'),
                    "处理状态": status,
                    "备注": violation.get('bz', ''),
                    "原始数据": violation
                }
                export_data["违章记录"].append(record)
            
            filename = f"违章记录_{plate}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            file_path = os.path.join(self.temp_dir, filename)
            
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
            
            return file_path
            
        except Exception as e:
            print(f"导出JSON失败: {e}")
            return None
    
    def cleanup_temp_files(self, file_paths):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"清理临时文件失败: {e}")

    def _get_overall_status(self, clbj, jkbj, fkje, points=None):
        """获取总处理状态文本（与界面显示逻辑一致）"""
        try:
            fkje = int(fkje) if fkje else 0
        except (ValueError, TypeError):
            fkje = 0

        try:
            points_val = int(points) if points else 0
        except (ValueError, TypeError):
            points_val = 0

        # 判断是否有未处理的项目
        has_pending_points = points_val > 0 and clbj == '0'
        has_pending_fine = fkje > 0 and jkbj == '0'

        if has_pending_points and has_pending_fine:
            return "未处理"
        elif has_pending_points:
            return "记分待处理"
        elif has_pending_fine:
            return "待缴款"
        else:
            # 无未处理项目
            if points_val == 0 and fkje == 0:
                return "无需处理"  # 警告类违章
            else:
                return "已完结"


def install_openpyxl():
    """安装openpyxl的辅助函数"""
    try:
        import subprocess
        import sys
        
        print("正在安装openpyxl...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl"])
        print("openpyxl安装成功！")
        return True
    except Exception as e:
        print(f"openpyxl安装失败: {e}")
        return False
