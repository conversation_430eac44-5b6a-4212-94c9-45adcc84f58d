#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车辆管理核心模块
负责车辆信息的加载、保存和管理
"""

import os
import json
import sys # 添加 sys 导入

# VEHICLES_FILE = "vehicles.json" # 旧的定义将移动并修改

def get_resource_path(relative_path):
    """ 获取资源的绝对路径，适用于开发环境和PyInstaller打包环境 """
    try:
        # PyInstaller 会创建一个临时文件夹并将路径存储在 _MEIPASS
        base_path = sys._MEIPASS
    except AttributeError:
        # 在开发环境中，使用脚本所在的目录
        base_path = os.path.abspath(os.path.dirname(__file__))
    return os.path.join(base_path, relative_path)

class VehicleManager:
    """车辆管理类，处理车辆信息的增删改查及持久化"""
    
    def __init__(self):
        """初始化车辆管理器"""
        # 使用 get_resource_path 来确定 vehicles.json 的绝对路径
        self.vehicles_file_path = get_resource_path("vehicles.json")
        self.vehicles = self.load_vehicles()
        # 确保按车牌排序
        self.vehicles.sort(key=lambda v: v.get('plate', ''))

    def load_vehicles(self):
        """从 JSON 文件加载车辆列表
        
        Returns:
            list: 车辆信息字典列表
        """
        if os.path.exists(self.vehicles_file_path): # 使用新的路径变量
            try:
                with open(self.vehicles_file_path, 'r', encoding='utf-8') as f: # 使用新的路径变量
                    data = json.load(f)
                    # 验证数据格式是否为列表
                    if isinstance(data, list):
                        # 验证列表中的每个元素是否为字典
                        valid_data = [
                            item for item in data 
                            if isinstance(item, dict) and 'plate' in item
                        ]
                        return valid_data
                    else:
                        print(f"警告: {self.vehicles_file_path} 格式错误，应为列表。将使用空列表。") # 使用新的路径变量
                        return []
            except json.JSONDecodeError:
                print(f"警告: {self.vehicles_file_path} 文件解析失败，将使用空列表。") # 使用新的路径变量
                return []
            except Exception as e:
                print(f"加载车辆文件时出错: {e}，将使用空列表。")
                return []
        return [] # 文件不存在，返回空列表

    def save_vehicles(self):
        """将车辆列表保存到 JSON 文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 保存前再次排序
            self.vehicles.sort(key=lambda v: v.get('plate', ''))
            with open(self.vehicles_file_path, 'w', encoding='utf-8') as f: # 使用新的路径变量
                json.dump(self.vehicles, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存车辆文件时出错: {e}")
            return False

    def get_all_vehicles(self):
        """获取所有车辆信息
        
        Returns:
            list: 车辆信息字典列表 (已排序)
        """
        return self.vehicles

    def add_vehicle(self, plate, vehicle_type, company, unique_id="", driver=""):
        """添加新车辆
        
        Args:
            plate (str): 车牌号
            vehicle_type (str): 车辆类型
            company (str): 所属公司
            unique_id (str, optional): 唯一标志. 默认为 "".
            driver (str, optional): 司机. 默认为 "".
            
        Returns:
            tuple: (是否成功, 错误或成功消息)
        """
        plate = plate.strip().upper()
        if not plate:
            return False, "车牌号不能为空"
        
        # 检查车牌号是否已存在
        if any(v['plate'] == plate for v in self.vehicles):
            return False, f"车牌号 {plate} 已存在"
            
        new_vehicle = {
            "plate": plate,
            "type": vehicle_type.strip(),
            "company": company.strip(),
            "unique_id": unique_id.strip(),
            "driver": driver.strip()
        }
        self.vehicles.append(new_vehicle)
        if self.save_vehicles():
            return True, "车辆添加成功"
        else:
            # 如果保存失败，回滚添加操作
            self.vehicles.pop()
            return False, "保存车辆列表失败"

    def update_vehicle(self, original_plate, new_plate, new_vehicle_type, new_company, new_unique_id="", new_driver=""):
        """更新车辆信息
        
        Args:
            original_plate (str): 要更新的车辆的原始车牌号
            new_plate (str): 新的车牌号
            new_vehicle_type (str): 新的车辆类型
            new_company (str): 新的所属公司
            new_unique_id (str, optional): 新的唯一标志. 默认为 "".
            new_driver (str, optional): 新的司机. 默认为 "".
            
        Returns:
            tuple: (是否成功, 错误或成功消息)
        """
        original_plate = original_plate.strip().upper()
        new_plate = new_plate.strip().upper()
        
        if not new_plate:
            return False, "新的车牌号不能为空"
            
        vehicle_to_update = None
        original_index = -1
        for i, v in enumerate(self.vehicles):
            if v['plate'] == original_plate:
                vehicle_to_update = v
                original_index = i
                break
        
        if not vehicle_to_update:
            return False, f"未找到要更新的车辆: {original_plate}"
            
        # 如果车牌号被修改，检查新的车牌号是否已存在 (且不是当前车辆自己)
        if original_plate != new_plate and any(v['plate'] == new_plate for v in self.vehicles):
            return False, f"新的车牌号 {new_plate} 已被其他车辆使用"
            
        # 创建原始数据的备份以备回滚
        original_data = vehicle_to_update.copy()
        
        # 更新车辆信息
        vehicle_to_update['plate'] = new_plate
        vehicle_to_update['type'] = new_vehicle_type.strip()
        vehicle_to_update['company'] = new_company.strip()
        vehicle_to_update['unique_id'] = new_unique_id.strip()
        vehicle_to_update['driver'] = new_driver.strip()
        
        if self.save_vehicles():
            return True, "车辆更新成功"
        else:
            # 保存失败，恢复原始数据
            self.vehicles[original_index] = original_data
            # 重新排序以防万一
            self.vehicles.sort(key=lambda v: v.get('plate', ''))
            return False, "保存车辆列表失败"

    def delete_vehicle(self, plate):
        """删除车辆信息
        
        Args:
            plate (str): 要删除的车牌号
            
        Returns:
            tuple: (是否成功, 错误或成功消息)
        """
        plate = plate.strip().upper()
        original_length = len(self.vehicles)
        vehicle_to_remove = None
        original_index = -1

        for i, v in enumerate(self.vehicles):
            if v['plate'] == plate:
                vehicle_to_remove = v
                original_index = i
                break

        if vehicle_to_remove is None:
            return False, f"未找到要删除的车辆: {plate}"

        # 从列表中移除
        self.vehicles.pop(original_index)
        
        if len(self.vehicles) == original_length -1:
            if self.save_vehicles():
                return True, "车辆删除成功"
            else:
                # 保存失败，恢复删除的车辆
                self.vehicles.insert(original_index, vehicle_to_remove)
                # 重新排序
                self.vehicles.sort(key=lambda v: v.get('plate', ''))
                return False, "保存车辆列表失败"
        else:
            # 理论上不应发生，但作为健壮性检查
            return False, "删除车辆时发生未知错误"
            
    def clear_vehicles(self):
        """清空所有车辆信息
        
        Returns:
            tuple: (是否成功, 错误或成功消息)
        """
        original_vehicles = self.vehicles.copy()
        self.vehicles = []
        
        if self.save_vehicles():
            return True, "所有车辆信息已清空"
        else:
            # 保存失败，恢复数据
            self.vehicles = original_vehicles
            return False, "清空车辆列表失败" 