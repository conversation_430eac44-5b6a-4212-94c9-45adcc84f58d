#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
违章查询核心模块
实现与12123网站API的交互，查询违章记录和记分信息
"""

import datetime
import requests
import time
from concurrent.futures import ThreadPoolExecutor
from utils.constants import PROVINCE_CODES, VIOLATION_POINTS

class ViolationQueryService:
    """违章查询服务类"""

    def __init__(self, on_status_update=None, data_manager=None):
        """初始化查询服务

        Args:
            on_status_update: 状态更新回调函数，用于通知UI更新状态
            data_manager: 数据管理器实例，用于记录查询失败信息
        """
        self.on_status_update = on_status_update
        self.data_manager = data_manager
        self.is_querying = False
        self.violations = []

        # 多Cookie管理
        self.cookies = []  # Cookie列表
        self.current_cookie_index = 0  # 当前使用的Cookie索引
        self.cookie_failure_counts = {}  # 每个Cookie的失败次数统计
        self.max_failures_per_cookie = 3  # 单个Cookie最大失败次数
    
    def update_status(self, message):
        """更新查询状态

        Args:
            message: 状态消息
        """
        if self.on_status_update:
            self.on_status_update(message)

    def set_cookies(self, cookies):
        """
        设置多个Cookie用于轮询。

        Args:
            cookies (list): Cookie字符串列表
        """
        if isinstance(cookies, str):
            cookies = [cookies]

        self.cookies = [cookie.strip() for cookie in cookies if cookie.strip()]
        self.current_cookie_index = 0
        self.cookie_failure_counts = {i: 0 for i in range(len(self.cookies))}
        self.update_status(f"已设置 {len(self.cookies)} 个Cookie用于轮询查询")

    def _get_next_available_cookie(self):
        """
        获取下一个可用的Cookie。

        Returns:
            tuple: (cookie_string, cookie_index) 或 (None, -1) 如果没有可用Cookie
        """
        if not self.cookies:
            return None, -1

        # 尝试找到失败次数未达到上限的Cookie
        for attempt in range(len(self.cookies)):
            cookie_index = (self.current_cookie_index + attempt) % len(self.cookies)
            failure_count = self.cookie_failure_counts.get(cookie_index, 0)

            if failure_count < self.max_failures_per_cookie:
                self.current_cookie_index = cookie_index
                return self.cookies[cookie_index], cookie_index

        # 如果所有Cookie都达到失败上限，重置失败计数并使用第一个
        self.update_status("所有Cookie都达到失败上限，重置失败计数")
        self.cookie_failure_counts = {i: 0 for i in range(len(self.cookies))}
        self.current_cookie_index = 0
        return self.cookies[0], 0

    def _record_cookie_failure(self, cookie_index, error_message):
        """
        记录Cookie失败。

        Args:
            cookie_index (int): Cookie索引
            error_message (str): 错误信息
        """
        if cookie_index >= 0 and cookie_index < len(self.cookies):
            self.cookie_failure_counts[cookie_index] = self.cookie_failure_counts.get(cookie_index, 0) + 1
            failure_count = self.cookie_failure_counts[cookie_index]
            self.update_status(f"Cookie #{cookie_index + 1} 失败 (第{failure_count}次): {error_message}")

            # 如果当前Cookie失败次数达到上限，切换到下一个
            if failure_count >= self.max_failures_per_cookie:
                self.update_status(f"Cookie #{cookie_index + 1} 已达到最大失败次数，将切换Cookie")
                self.current_cookie_index = (cookie_index + 1) % len(self.cookies)

    def _record_cookie_success(self, cookie_index):
        """
        记录Cookie成功，重置其失败计数。

        Args:
            cookie_index (int): Cookie索引
        """
        if cookie_index >= 0 and cookie_index < len(self.cookies):
            self.cookie_failure_counts[cookie_index] = 0

    def _is_rate_limit_error(self, error_message):
        """
        判断是否是查询频繁错误。

        Args:
            error_message (str): 错误信息

        Returns:
            bool: 是否是查询频繁错误
        """
        rate_limit_keywords = [
            "查询频繁", "请求过于频繁", "访问频率过高", "请稍后再试",
            "too many requests", "rate limit", "频率限制"
        ]
        return any(keyword in error_message.lower() for keyword in rate_limit_keywords)
    
    def start_query(self, plates, cookie, province_code, start_date, end_date, max_threads=3, query_interval=0.1, progress_callback=None):
        """开始查询违章记录

        Args:
            plates: 车牌列表
            cookie: 登录Cookie (如果设置了多Cookie，此参数会被忽略)
            province_code: 省份代码
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            max_threads: 最大线程数
            query_interval: 查询任务提交间隔时间（秒）
            progress_callback: 进度回调函数，接收(current, total, status)参数

        Returns:
            list: 查询到的违章记录列表
        """
        # 参数验证和限制
        max_threads = min(max_threads, 3)  # 限制最大线程数为3
        query_interval = max(query_interval, 0.1)  # 限制最小查询间隔为0.1秒

        self.is_querying = True
        self.violations = []

        # 如果设置了多Cookie，使用轮询机制
        if self.cookies:
            self.update_status(f"使用 {len(self.cookies)} 个Cookie进行轮询查询 (线程数: {max_threads}, 间隔: {query_interval}秒)")
        else:
            # 如果没有设置多Cookie，使用传入的单个Cookie
            self.set_cookies([cookie])
        
        try:
            # 创建线程池
            with ThreadPoolExecutor(max_workers=max_threads) as executor:
                # 第一步：查询所有车牌的违章记录
                futures = []
                for plate in plates:
                    if not self.is_querying:
                        break
                    
                    futures.append(executor.submit(
                        self.query_violations,
                        plate,
                        province_code,
                        start_date,
                        end_date,
                        ""  # 传空字符串，让方法内部使用Cookie轮询
                    ))
                    
                    # 更新状态
                    self.update_status(f"正在查询违章记录: {plate}")
                    # 让UI有时间响应
                    time.sleep(query_interval)
                
                # 收集所有违章记录查询结果
                all_violations = []
                for i, future in enumerate(futures):
                    if not self.is_querying:
                        break

                    plate = plates[i]
                    try:
                        result = future.result()
                        if result["success"]:
                            violations_data = result["data"]
                            if violations_data:
                                # 添加车牌信息和查询省份代码到违章记录
                                for v in violations_data:
                                    v["_plate"] = plate
                                    v["_query_province_code"] = province_code
                                all_violations.extend(violations_data)

                                # 更新UI
                                self.update_status(f"发现 {plate} 的 {len(violations_data)} 条违章记录")
                            else:
                                self.update_status(f"{plate} 没有违章记录")
                        else:
                            error_msg = result['message']
                            self.update_status(f"查询 {plate} 失败: {error_msg}")
                            # 记录查询失败信息
                            if self.data_manager:
                                query_params = {
                                    'province_code': province_code,
                                    'start_date': start_date,
                                    'end_date': end_date
                                }
                                self.data_manager.add_failed_query(plate, error_msg, query_params)
                    except Exception as e:
                        error_msg = f"处理 {plate} 的结果时出错: {str(e)}"
                        self.update_status(error_msg)
                        # 记录查询异常信息
                        if self.data_manager:
                            query_params = {
                                'province_code': province_code,
                                'start_date': start_date,
                                'end_date': end_date
                            }
                            self.data_manager.add_failed_query(plate, error_msg, query_params)

                    # 更新进度
                    if progress_callback:
                        progress_callback(i + 1, len(plates), f"已查询 {i + 1}/{len(plates)} 个车牌")
                
                self.violations = all_violations
                
                # 如果没有违章记录，直接结束
                if not all_violations:
                    self.update_status("查询完成，没有发现违章记录")
                    self.is_querying = False
                    return []
                
                # 第二步：查询记分 - 优化查询过程，对相同违法代码只查询一次
                self.update_status(f"发现 {len(all_violations)} 条违章记录，开始查询记分...")
                
                # 创建违法代码-记分映射缓存
                wfxw_points_cache = {}
                
                # 对违章记录按违法代码分组
                wfxw_groups = {}
                for violation in all_violations:
                    wfxw = violation.get("wfxw", "")
                    if wfxw:
                        # 使用 (wfxw, _query_province_code)作为键，确保不同省份的相同代码分别查询
                        cache_key = (wfxw, violation.get("_query_province_code")) 
                        if cache_key not in wfxw_groups:
                            wfxw_groups[cache_key] = []
                        wfxw_groups[cache_key].append(violation)
                
                # 清空之前的futures
                futures = []
                
                # 只为每种 (违法代码, 省份) 组合查询一次记分
                wfxw_to_query = []
                for cache_key, violations_in_group in wfxw_groups.items():
                    if not self.is_querying:
                        break
                    
                    # 选择该组合的第一条记录用于查询
                    wfxw_to_query.append((cache_key, violations_in_group[0])) 
                
                total_types = len(wfxw_to_query)
                self.update_status(f"需要查询 {total_types} 种不同的违法类型记分...")
                
                # 为每种违法类型创建一个查询任务
                for i, (cache_key, violation_for_query) in enumerate(wfxw_to_query):
                    if not self.is_querying:
                        break
                    
                    wfxw, query_province_code = cache_key
                    futures.append(executor.submit(
                        self.query_points,
                        violation_for_query,
                        ""  # 传空字符串，让方法内部使用Cookie轮询
                    ))
                    
                    # 更新状态
                    plate = violation_for_query.get("_plate", "未知车牌")
                    self.update_status(f"正在查询记分: {plate} - {wfxw} ({i+1}/{total_types})")
                    # 让UI有时间响应
                    time.sleep(query_interval)
                
                # 收集所有记分查询结果并更新缓存
                for i, future in enumerate(futures):
                    if not self.is_querying:
                        break
                    
                    cache_key, _ = wfxw_to_query[i]
                    
                    try:
                        result = future.result()
                        points = "0"
                        if result["success"]:
                            points = result.get("points", "0")
                        
                        # 更新缓存
                        wfxw_points_cache[cache_key] = points
                        
                    except Exception as e:
                        self.update_status(f"处理违法代码 {cache_key[0]} ({cache_key[1]}) 的记分结果时出错: {str(e)}")
                
                # 应用缓存的记分结果到所有违章记录
                for violation in all_violations:
                    wfxw = violation.get("wfxw", "")
                    query_province_code = violation.get("_query_province_code")
                    cache_key = (wfxw, query_province_code)
                    points = wfxw_points_cache.get(cache_key, "0")
                    
                    # 更新记分信息
                    violation["_points"] = points
                
                self.update_status(f"查询完成，共发现 {len(all_violations)} 条违章记录，涉及 {len(wfxw_points_cache)} 种违法类型组合")
                return all_violations
                
        except Exception as e:
            self.update_status(f"查询过程中出错: {str(e)}")
            return []
        finally:
            self.is_querying = False
    
    def stop_query(self):
        """停止查询过程"""
        self.is_querying = False
        self.update_status("用户停止查询")
    
    def query_violations(self, plate, province_code, start_date, end_date, cookie, retry_count=0):
        """查询违章记录

        Args:
            plate: 车牌号码
            province_code: 省份代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            cookie: 登录Cookie (如果为空且设置了多Cookie，则使用轮询)
            retry_count: 重试次数

        Returns:
            dict: 包含查询结果的字典
        """
        # 如果设置了多Cookie，使用轮询机制
        if self.cookies and not cookie:
            actual_cookie, cookie_index = self._get_next_available_cookie()
            if actual_cookie is None:
                return {"success": False, "data": None, "message": "没有可用的Cookie"}
        else:
            actual_cookie = cookie
            cookie_index = -1
        try:
            # 验证日期格式
            today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            try:
                start_datetime = datetime.datetime.strptime(start_date, "%Y-%m-%d")
                end_datetime = datetime.datetime.strptime(end_date, "%Y-%m-%d")
                
                # 检查日期范围是否合理
                date_diff = (end_datetime - start_datetime).days
                if date_diff < 0:
                    return {"success": False, "data": None, "message": "开始日期不能大于结束日期"}
                
                # 确保结束日期不超过当前日期
                if end_datetime > today:
                    end_datetime = today
                    end_date = today.strftime("%Y-%m-%d")
                    # print(f"结束日期调整为今天: {end_date}") # 日志过于频繁，暂时注释
                
            except ValueError:
                return {"success": False, "data": None, "message": "日期格式错误，请使用YYYY-MM-DD格式"}
            
            # 转换日期格式 (YYYY-MM-DD -> YYYYMMDD)
            start_date_fmt = start_date.replace('-', '')
            end_date_fmt = end_date.replace('-', '')
            
            # print(f"查询日期范围: {start_date_fmt} 至 {end_date_fmt}") # 日志过于频繁
            
            # API端点
            url = f"https://{province_code}.122.gov.cn/user/m/uservio/suriquery"
            
            # 请求头
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Cookie": actual_cookie,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36"
            }
            
            # 请求体
            data = {
                "startDate": start_date_fmt,
                "endDate": end_date_fmt,
                "hpzl": "52",  # 小型汽车
                "hphm": plate,
                "page": "1",
                "type": "0"
            }
            
            # print(f"发送违章查询请求: URL={url}, 参数={data}") # 日志过于频繁
            
            # 发送请求
            response = requests.post(url, headers=headers, data=data, timeout=30)
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return {"success": False, "data": None, "message": f"HTTP错误: {response.status_code}"}
                
            try:
                response_data = response.json()
            except Exception as e:
                # print(f"原始响应: {response.text[:200]}") # 调试时使用
                return {"success": False, "data": None, "message": f"JSON解析错误: {str(e)}"}
            
            # 检查响应状态
            if response_data.get("code") == 200:
                # 记录Cookie成功
                if cookie_index >= 0:
                    self._record_cookie_success(cookie_index)

                violations_content = response_data.get("data", {}).get("content", [])
                total_count = response_data.get("data", {}).get("totalCount", 0)
                
                # 检查是否有分页
                total_pages = response_data.get("data", {}).get("totalPages", 1)
                if total_pages > 1:
                    # print(f"检测到多页结果: 共{total_pages}页，总计{total_count}条记录")
                    
                    # 获取第一页之后的所有页
                    for page_num in range(2, total_pages + 1):
                        data["page"] = str(page_num)
                        try:
                            page_response = requests.post(url, headers=headers, data=data, timeout=30)
                            page_data_json = page_response.json()
                            
                            if page_data_json.get("code") == 200:
                                page_violations = page_data_json.get("data", {}).get("content", [])
                                violations_content.extend(page_violations)
                                # print(f"已获取第{page_num}页数据，共{len(page_violations)}条")
                            else:
                                print(f"获取第{page_num}页数据失败: {page_data_json.get('message')}")
                        except Exception as e_page:
                            print(f"获取第{page_num}页数据出错: {str(e_page)}")
                
                # 过滤违章记录，确保日期在合理范围内
                filtered_violations = []
                for violation_item in violations_content:
                    try:
                        wfsj = violation_item.get("wfsj", "")  # 违法时间
                        if not wfsj:
                            continue
                            
                        # 提取日期部分 (违法时间格式通常为: 2023-01-01 12:34)
                        violation_date_str = wfsj.split(" ")[0] if " " in wfsj else wfsj
                        violation_date_obj = datetime.datetime.strptime(violation_date_str, "%Y-%m-%d")
                        
                        # 检查日期是否在范围内，并确保不是未来日期
                        if violation_date_obj <= today and violation_date_obj >= start_datetime and violation_date_obj <= end_datetime:
                            filtered_violations.append(violation_item)
                        # else:
                            # print(f"排除范围外或未来日期的违章记录: {wfsj}") # 日志过于频繁
                    except Exception as e_filter:
                        print(f"处理违章日期时出错，记录已跳过: {str(e_filter)}")
                
                # if len(filtered_violations) < len(violations_content):
                    # print(f"已过滤不符合日期范围的记录: 原始{len(violations_content)}条 -> 有效{len(filtered_violations)}条")
                
                return {"success": True, "data": filtered_violations, "message": f"查询成功，共{len(filtered_violations)}条有效记录"}
            else:
                error_msg = response_data.get("message", "未知错误")

                # 检查是否是查询频繁错误，如果是则尝试切换Cookie
                if self._is_rate_limit_error(error_msg) and cookie_index >= 0 and retry_count < len(self.cookies):
                    self._record_cookie_failure(cookie_index, error_msg)
                    self.update_status(f"检测到查询频繁错误，尝试切换Cookie重试 (第{retry_count + 1}次)")
                    time.sleep(1)  # 短暂延迟
                    return self.query_violations(plate, province_code, start_date, end_date, "", retry_count + 1)

                # 记录Cookie失败
                if cookie_index >= 0:
                    self._record_cookie_failure(cookie_index, error_msg)

                return {"success": False, "data": None, "message": error_msg}
                
        except requests.exceptions.Timeout:
            error_msg = "查询超时"
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg}
        except requests.exceptions.RequestException as e_req:
            error_msg = f"请求错误: {str(e_req)}"
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg}
        except Exception as e_main:
            error_msg = f"查询出错: {str(e_main)}"
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg}
    
    def query_points(self, violation, cookie):
        """查询违章记分

        Args:
            violation: 违章记录对象 (包含 _query_province_code)
            cookie: 登录Cookie (如果为空且设置了多Cookie，则使用轮询)

        Returns:
            dict: 包含记分结果的字典
        """
        # 如果设置了多Cookie，使用轮询机制
        if self.cookies and not cookie:
            actual_cookie, cookie_index = self._get_next_available_cookie()
            if actual_cookie is None:
                return {"success": False, "points": None, "message": "没有可用的Cookie"}
        else:
            actual_cookie = cookie
            cookie_index = -1
        try:
            # 从违章记录中获取必要信息
            wfxw = violation.get("wfxw")
            xh = violation.get("xh")
            cjjg = violation.get("cjjg")
            hphm = violation.get("hphm") 
            hpzl = violation.get("hpzl", "52") 
            
            # 使用存储在violation记录中的查询省份代码
            province_code_for_query = violation.get("_query_province_code")
            
            if not all([xh, cjjg, province_code_for_query, hphm]):
                return {"success": False, "points": None, "message": "缺少查询记分所需的参数(xh, cjjg, province, hphm)"}
            
            # print(f"记分查询参数: wfxw={wfxw}, xh={xh}, cjjg={cjjg}, province={province_code_for_query}, hphm={hphm}, hpzl={hpzl}")
            
            # API URL
            url = f"https://{province_code_for_query}.122.gov.cn/user/m/tsc/vio/querySurvielDetail"
            # print(f"使用API URL: {url}")
            
            # 准备请求头
            headers = {
                "Cookie": actual_cookie,
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0",
                "Accept": "application/json"
            }
            
            # 准备POST请求数据
            data = {
                "hphm": hphm,
                "hpzl": hpzl,
                "xh": xh,
                "cjjg": cjjg
            }
            
            # print(f"发送POST请求: {url}") # 日志过于频繁
            response = requests.post(url, headers=headers, data=data, timeout=30)
            
            # print(f"状态码: {response.status_code}") # 日志过于频繁
            if response.status_code != 200:
                # print(f"HTTP状态码错误: {response.status_code}")
                # print(f"响应内容前100个字符: {response.text[:100]}")
                
                estimated_points = self._estimate_points_by_wfxw(wfxw)
                points_str = str(estimated_points) if estimated_points is not None else "0"
                return {"success": True, "points": points_str, "message": "HTTP错误，使用备用方法估算记分"}
            
            try:
                response_data = response.json()
            except Exception as e_json_parse:
                # print(f"JSON解析错误: {str(e_json_parse)}")
                # print(f"响应内容前100个字符: {response.text[:100]}")
                
                estimated_points = self._estimate_points_by_wfxw(wfxw)
                points_str = str(estimated_points) if estimated_points is not None else "0"
                return {"success": True, "points": points_str, "message": "JSON解析错误，使用备用方法估算记分"}
            
            if response_data.get("code") == 200:
                detail_data = response_data.get("data", {})
                points_val = detail_data.get("wfjfs")
                
                if points_val is None:
                    estimated_points = self._estimate_points_by_wfxw(wfxw)
                    if estimated_points is not None and estimated_points > 0:
                        # print(f"API未返回记分，根据违法代码估算记分: {estimated_points}")
                        points_val = str(estimated_points)
                    else:
                        points_val = "0"
                
                # print(f"查询到记分: {points_val}")
                return {"success": True, "points": str(points_val), "message": "查询成功"}
            else:
                error_msg = response_data.get("message", "未知错误")
                # print(f"记分查询错误: {error_msg}")
                
                estimated_points = self._estimate_points_by_wfxw(wfxw)
                if estimated_points is not None and estimated_points > 0:
                    # print(f"API返回错误，根据违法代码估算记分: {estimated_points}")
                    points_str = str(estimated_points)
                    return {"success": True, "points": points_str, "message": "API错误，使用备用方法估算记分"}
                
                return {"success": False, "points": None, "message": error_msg}

        except requests.exceptions.Timeout:
            return {"success": True, "points": str(self._estimate_points_by_wfxw(wfxw) or "0"), "message": "记分查询超时，使用估算值"}
        except requests.exceptions.RequestException as e_req_points:
            return {"success": True, "points": str(self._estimate_points_by_wfxw(wfxw) or "0"), "message": f"记分请求错误，使用估算值: {str(e_req_points)}"}
        except Exception as e_points_main:
            # print(f"记分查询异常: {str(e_points_main)}")
            
            try:
                estimated_points = self._estimate_points_by_wfxw(wfxw)
                if estimated_points is not None and estimated_points > 0:
                    # print(f"出现异常，根据违法代码估算记分: {estimated_points}")
                    points_str = str(estimated_points)
                    return {"success": True, "points": points_str, "message": "记分查询异常，使用备用方法估算记分"}
            except:
                pass # 确保即使估算也出错，也能返回
                
            return {"success": False, "points": None, "message": f"记分查询出错: {str(e_points_main)}"}
    
    def _estimate_points_by_wfxw(self, wfxw):
        """根据违法行为代码估算记分
        
        Args:
            wfxw: 违法行为代码
            
        Returns:
            int or None: 估算的记分, 如果无法估算则为None或0
        """
        if not wfxw or not isinstance(wfxw, str):
            return 0 # 或 None，取决于希望如何处理无效输入

        base_wfxw = ''.join(c for c in wfxw if c.isdigit())
        
        points = VIOLATION_POINTS.get(base_wfxw)
        if points is None:
            points = VIOLATION_POINTS.get(wfxw, 0) # 默认0分如果都没匹配到
        
        # 特殊规则覆盖，确保它们有更高优先级
        if wfxw.startswith("1344"): points = 3
        elif wfxw.startswith("1301"): points = 12
        elif wfxw.startswith("1208"): points = 12
        elif wfxw.startswith("1108"): points = 3
        
        return points
    
    def _get_province_code_from_violation(self, violation):
        """优先从违章记录中获取查询时使用的省份代码，其次尝试从cjjg推断。
        
        Args:
            violation: 违章记录对象
            
        Returns:
            str or None: 省份代码
        """
        # 优先使用查询时记录的省份代码
        query_province_code = violation.get("_query_province_code")
        if query_province_code:
            return query_province_code
        
        # 如果没有，尝试从cjjg推断 (这是一个备用逻辑，可能不总是准确)
        # cjjg = violation.get("cjjg", "")
        # if cjjg and len(cjjg) >= 6: # cjjg 通常是行政区划代码，例如江苏南京是320100
        #     # 查找前两位或前四位是否匹配省份
        #     # 注意：PROVINCE_CODES 的键是中文名，值是字母代码
        #     # 这里需要一个反向映射或者根据行政区划代码规则来判断
        #     # 例如，江苏省的行政区划代码以32开头
        #     # 这是一个复杂的逻辑，暂时简化
        #     pass 
            
        # 最终的备用，如果实在无法判断，返回一个默认值或None
        # print("警告: 未能在违章记录中找到_query_province_code，记分查询可能不准确。")
        return None # 或者一个默认的省份代码，但这可能导致错误 