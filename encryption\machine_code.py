import wmi
import hashlib
import os
import json
import uuid
from encryption.config import AUTH_CONFIG

class MachineCodeGenerator:
    def __init__(self):
        self.pre_str = AUTH_CONFIG["pre_str"]
        self.suf_str = AUTH_CONFIG["suf_str"]
        self.m_wmi = wmi.WMI()
        self._cached_components = None
        self._cache_file = self._get_cache_file_path()

    def _get_cache_file_path(self):
        """获取缓存文件路径"""
        # 使用系统临时目录存储缓存
        import tempfile
        cache_dir = os.path.join(tempfile.gettempdir(), "YiChengTech")
        os.makedirs(cache_dir, exist_ok=True)
        return os.path.join(cache_dir, "machine_components.cache")

    def _load_cached_components(self):
        """加载缓存的硬件组件信息"""
        try:
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载缓存失败: {e}")
        return None

    def _save_cached_components(self, components):
        """保存硬件组件信息到缓存"""
        try:
            with open(self._cache_file, 'w', encoding='utf-8') as f:
                json.dump(components, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存失败: {e}")

    def get_cpu_serial(self):
        """获取CPU序列号 - 优化版本"""
        try:
            import threading
            import subprocess

            result = {'serial': None, 'error': None}

            def _get_cpu_with_timeout():
                try:
                    # 方法1：使用wmic命令（更快）
                    try:
                        output = subprocess.check_output([
                            'wmic', 'cpu', 'get', 'processorid', '/format:csv'
                        ], universal_newlines=True, timeout=2)

                        lines = output.strip().split('\n')[1:]
                        for line in lines:
                            if line.strip():
                                parts = line.split(',')
                                if len(parts) >= 2:
                                    serial = parts[1].strip()
                                    if serial and serial != "ProcessorId":
                                        result['serial'] = serial
                                        return
                    except:
                        pass

                    # 方法2：回退到WMI
                    cpu_info = self.m_wmi.Win32_Processor()
                    if len(cpu_info) > 0 and cpu_info[0].ProcessorId:
                        result['serial'] = cpu_info[0].ProcessorId.strip()

                except Exception as e:
                    result['error'] = str(e)

            thread = threading.Thread(target=_get_cpu_with_timeout)
            thread.daemon = True
            thread.start()
            thread.join(timeout=3)

            if result['serial']:
                return result['serial']
            elif result['error']:
                print(f"获取CPU序列号错误: {result['error']}")

            return "ABCDEFGHIJKLMNOP"

        except Exception as e:
            print(f"获取CPU序列号错误: {e}")
            return "ABCDEFGHIJKLMNOP"

    def get_disk_serial(self):
        """获取硬盘序列号 - 优化版本"""
        try:
            import threading
            import subprocess

            result = {'serial': None, 'error': None}

            def _get_disk_with_timeout():
                try:
                    # 方法1：使用wmic命令（更快）
                    try:
                        output = subprocess.check_output([
                            'wmic', 'diskdrive', 'get', 'serialnumber', '/format:csv'
                        ], universal_newlines=True, timeout=3)

                        lines = output.strip().split('\n')[1:]  # 跳过标题行
                        for line in lines:
                            if line.strip():
                                parts = line.split(',')
                                if len(parts) >= 2:
                                    serial = parts[1].strip()
                                    if serial and serial not in ["", "SerialNumber"]:
                                        result['serial'] = serial
                                        print(f"获取磁盘序列号 (wmic): {serial}")
                                        return
                    except:
                        pass

                    # 方法2：回退到WMI（较慢但更可靠）
                    # 简化的WMI查询，只获取第一个可用的磁盘序列号
                    disk_info = self.m_wmi.Win32_DiskDrive()
                    for disk in disk_info:
                        if disk.SerialNumber:
                            serial = disk.SerialNumber.strip()
                            if serial:
                                result['serial'] = serial
                                print(f"获取磁盘序列号 (WMI): {serial}")
                                return

                except Exception as e:
                    result['error'] = str(e)

            # 使用线程和超时机制
            thread = threading.Thread(target=_get_disk_with_timeout)
            thread.daemon = True
            thread.start()
            thread.join(timeout=4)  # 4秒超时

            if result['serial']:
                return result['serial']
            elif result['error']:
                print(f"获取磁盘序列号错误: {result['error']}")
            else:
                print("获取磁盘序列号超时，使用默认值")

            return "DEFAULT_SERIAL"

        except Exception as e:
            print(f"获取磁盘序列号错误: {str(e)}")
            return "DEFAULT_SERIAL"

    def get_stable_mac_address(self):
        """获取最稳定的MAC地址 - 优化版本"""
        try:
            import threading
            import time

            result = {'mac': None, 'error': None}

            def _get_mac_with_timeout():
                try:
                    # 使用更快的方法获取MAC地址
                    import uuid
                    import subprocess

                    # 方法1：使用uuid.getnode()获取主MAC地址
                    try:
                        mac_int = uuid.getnode()
                        mac_hex = f"{mac_int:012x}".upper()
                        if mac_hex != "000000000000":
                            result['mac'] = mac_hex
                            print(f"选择MAC地址 (快速方法): {mac_hex}")
                            return
                    except:
                        pass

                    # 方法2：使用getmac命令（更快）
                    try:
                        output = subprocess.check_output(['getmac', '/fo', 'csv'],
                                                       universal_newlines=True,
                                                       timeout=3)
                        lines = output.strip().split('\n')[1:]  # 跳过标题行
                        for line in lines:
                            if line.strip():
                                parts = line.split(',')
                                if len(parts) >= 1:
                                    mac = parts[0].strip('"').replace('-', '').replace(':', '')
                                    if mac and mac != "000000000000":
                                        result['mac'] = mac
                                        print(f"选择MAC地址 (getmac): {mac}")
                                        return
                    except:
                        pass

                    # 方法3：回退到WMI（较慢但更详细）
                    network_configs = self.m_wmi.Win32_NetworkAdapterConfiguration()
                    mac_candidates = []

                    for network in network_configs:
                        if network.MacAddress and network.MacAddress != "00:00:00:00:00:00":
                            mac_info = {
                                'mac': network.MacAddress.replace(":", ""),
                                'description': network.Description or "",
                                'ip_enabled': network.IPEnabled,
                                'physical': 'Virtual' not in (network.Description or "")
                            }
                            mac_candidates.append(mac_info)

                    if mac_candidates:
                        # 排序优先级：物理网卡 > 启用IP > 以太网
                        mac_candidates.sort(key=lambda x: (
                            x['physical'],
                            x['ip_enabled'],
                            'Ethernet' in x['description'],
                            x['mac']
                        ), reverse=True)

                        selected_mac = mac_candidates[0]['mac']
                        result['mac'] = selected_mac
                        print(f"选择MAC地址 (WMI): {selected_mac} ({mac_candidates[0]['description']})")

                except Exception as e:
                    result['error'] = str(e)

            # 使用线程和超时机制
            thread = threading.Thread(target=_get_mac_with_timeout)
            thread.daemon = True
            thread.start()
            thread.join(timeout=5)  # 5秒超时

            if result['mac']:
                return result['mac']
            elif result['error']:
                print(f"获取MAC地址错误: {result['error']}")
            else:
                print("获取MAC地址超时，使用默认值")

            return "ABCDEF123456"

        except Exception as e:
            print(f"获取MAC地址错误: {e}")
            return "ABCDEF123456"

    def get_board_serial(self):
        """获取主板序列号 - 优化版本"""
        try:
            import threading
            import subprocess

            result = {'serial': None, 'error': None}

            def _get_board_with_timeout():
                try:
                    # 方法1：使用wmic命令（更快）
                    try:
                        output = subprocess.check_output([
                            'wmic', 'baseboard', 'get', 'serialnumber', '/format:csv'
                        ], universal_newlines=True, timeout=2)

                        lines = output.strip().split('\n')[1:]
                        for line in lines:
                            if line.strip():
                                parts = line.split(',')
                                if len(parts) >= 2:
                                    serial = parts[1].strip().strip('.')
                                    if serial and serial not in ["SerialNumber", "Default string", "To be filled by O.E.M.", "None", ""]:
                                        result['serial'] = serial
                                        return
                    except:
                        pass

                    # 方法2：回退到WMI
                    board_info = self.m_wmi.Win32_BaseBoard()
                    if len(board_info) > 0 and board_info[0].SerialNumber:
                        serial = board_info[0].SerialNumber.strip().strip('.')
                        if serial and serial not in ["Default string", "To be filled by O.E.M.", "None"]:
                            result['serial'] = serial

                except Exception as e:
                    result['error'] = str(e)

            thread = threading.Thread(target=_get_board_with_timeout)
            thread.daemon = True
            thread.start()
            thread.join(timeout=3)

            if result['serial']:
                return result['serial']
            elif result['error']:
                print(f"获取主板序列号错误: {result['error']}")

            return "ABCDEFGHIJKLMN"

        except Exception as e:
            print(f"获取主板序列号错误: {e}")
            return "ABCDEFGHIJKLMN"

    def get_system_uuid(self):
        """获取系统UUID作为额外的稳定标识"""
        try:
            computer_system = self.m_wmi.Win32_ComputerSystemProduct()
            if len(computer_system) > 0 and computer_system[0].UUID:
                system_uuid = computer_system[0].UUID.strip()
                if system_uuid and system_uuid != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF":
                    return system_uuid.replace("-", "")[:16]  # 取前16位
            return "DEFAULT_UUID_16"
        except Exception as e:
            print(f"获取系统UUID错误: {e}")
            return "DEFAULT_UUID_16"

    def get_hardware_components(self, use_cache=True, timeout=15):
        """获取硬件组件信息，支持缓存和总体超时控制"""
        if use_cache and self._cached_components:
            print("使用缓存的硬件组件信息")
            return self._cached_components

        if use_cache:
            cached = self._load_cached_components()
            if cached:
                print("从缓存文件加载硬件组件信息")
                self._cached_components = cached
                return cached

        print("开始获取硬件信息...")
        import threading
        import time

        components = {}
        start_time = time.time()

        def _get_all_components():
            try:
                print("  正在获取CPU序列号...")
                components['cpu_serial'] = self.get_cpu_serial()

                if time.time() - start_time > timeout:
                    print("  超时，使用默认值...")
                    return

                print("  正在获取磁盘序列号...")
                components['disk_serial'] = self.get_disk_serial()

                if time.time() - start_time > timeout:
                    print("  超时，使用默认值...")
                    return

                print("  正在获取MAC地址...")
                components['mac_address'] = self.get_stable_mac_address()

                if time.time() - start_time > timeout:
                    print("  超时，使用默认值...")
                    return

                print("  正在获取主板序列号...")
                components['board_serial'] = self.get_board_serial()

                print("  正在获取系统UUID...")
                components['system_uuid'] = self.get_system_uuid()

                components['timestamp'] = str(int(os.path.getctime(__file__)))

            except Exception as e:
                print(f"获取硬件信息时出错: {e}")

        # 使用线程和总体超时控制
        thread = threading.Thread(target=_get_all_components)
        thread.daemon = True
        thread.start()
        thread.join(timeout=timeout)

        # 确保所有必要的组件都有值
        default_components = {
            'cpu_serial': 'ABCDEFGHIJKLMNOP',
            'disk_serial': 'DEFAULT_SERIAL',
            'mac_address': 'ABCDEF123456',
            'board_serial': 'ABCDEFGHIJKLMN',
            'system_uuid': 'DEFAULT_UUID_16',
            'timestamp': str(int(time.time()))
        }

        for key, default_value in default_components.items():
            if key not in components or not components[key]:
                components[key] = default_value
                print(f"  使用默认值: {key} = {default_value}")

        elapsed_time = time.time() - start_time
        print(f"硬件信息获取完成，耗时: {elapsed_time:.2f}秒")

        self._cached_components = components
        if use_cache:
            self._save_cached_components(components)

        return components

    def get_machine_code(self, use_cache=True, fast_mode=None):
        """生成稳定的机器码"""
        try:
            # 检查是否启用快速模式
            if fast_mode is None:
                fast_mode = AUTH_CONFIG.get("fast_startup", True)

            if fast_mode and use_cache:
                # 快速模式：使用简化的硬件获取方法
                from encryption.fast_machine_code import FastMachineCodeGenerator
                fast_generator = FastMachineCodeGenerator()
                return fast_generator.get_machine_code(use_cache=True)

            # 标准模式：使用完整的硬件获取方法
            timeout = AUTH_CONFIG.get("hardware_timeout", 15)
            components = self.get_hardware_components(use_cache, timeout=timeout)

            # 使用多个硬件特征组合，提高稳定性
            combine_str = (
                self.pre_str +
                components['mac_address'] +
                components['cpu_serial'] +
                components['disk_serial'] +
                components['board_serial'] +
                components['system_uuid'] +
                self.suf_str
            )

            # 使用SHA-256替代MD5，提高安全性
            machine_code = hashlib.sha256(combine_str.encode("utf-8")).hexdigest().upper()
            return machine_code

        except Exception as e:
            print(f"生成机器码错误: {e}")
            # 如果出错，尝试使用快速模式
            if not fast_mode:
                try:
                    print("尝试使用快速模式生成机器码...")
                    from encryption.fast_machine_code import FastMachineCodeGenerator
                    fast_generator = FastMachineCodeGenerator()
                    return fast_generator.get_machine_code(use_cache=True)
                except:
                    pass

            # 最后的备用方案
            fallback_str = self.pre_str + "FALLBACK_MACHINE_CODE" + self.suf_str
            return hashlib.sha256(fallback_str.encode("utf-8")).hexdigest().upper()

    def clear_cache(self):
        """清除缓存，强制重新获取硬件信息"""
        self._cached_components = None
        try:
            if os.path.exists(self._cache_file):
                os.remove(self._cache_file)
                print("机器码缓存已清除")
        except Exception as e:
            print(f"清除缓存失败: {e}")

    def validate_machine_code_stability(self, iterations=3):
        """验证机器码稳定性"""
        codes = []
        for i in range(iterations):
            # 每次都重新获取（不使用缓存）
            code = self.get_machine_code(use_cache=False)
            codes.append(code)

        unique_codes = set(codes)
        is_stable = len(unique_codes) == 1

        if not is_stable:
            print(f"警告：机器码不稳定，发现 {len(unique_codes)} 个不同的机器码")
            for i, code in enumerate(codes):
                print(f"  第 {i+1} 次: {code}")

        return is_stable, codes[0] if codes else None