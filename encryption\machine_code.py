import wmi
import hashlib
import os
import json
import uuid
from encryption.config import AUTH_CONFIG

class MachineCodeGenerator:
    def __init__(self):
        self.pre_str = AUTH_CONFIG["pre_str"]
        self.suf_str = AUTH_CONFIG["suf_str"]
        self.m_wmi = wmi.WMI()
        self._cached_components = None
        self._cache_file = self._get_cache_file_path()

    def _get_cache_file_path(self):
        """获取缓存文件路径"""
        # 使用系统临时目录存储缓存
        import tempfile
        cache_dir = os.path.join(tempfile.gettempdir(), "YiChengTech")
        os.makedirs(cache_dir, exist_ok=True)
        return os.path.join(cache_dir, "machine_components.cache")

    def _load_cached_components(self):
        """加载缓存的硬件组件信息"""
        try:
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载缓存失败: {e}")
        return None

    def _save_cached_components(self, components):
        """保存硬件组件信息到缓存"""
        try:
            with open(self._cache_file, 'w', encoding='utf-8') as f:
                json.dump(components, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存失败: {e}")

    def get_cpu_serial(self):
        """获取CPU序列号(16位)"""
        try:
            cpu_info = self.m_wmi.Win32_Processor()
            if len(cpu_info) > 0 and cpu_info[0].ProcessorId:
                return cpu_info[0].ProcessorId.strip()
            return "ABCDEFGHIJKLMNOP"
        except Exception as e:
            print(f"获取CPU序列号错误: {e}")
            return "ABCDEFGHIJKLMNOP"

    def get_disk_serial(self):
        """获取硬盘序列号 - 选择最稳定的系统盘"""
        try:
            # 优先获取系统盘(C盘)的物理磁盘序列号
            logical_disks = self.m_wmi.Win32_LogicalDisk()
            system_disk_index = None

            for disk in logical_disks:
                if disk.DeviceID == "C:":
                    # 获取系统盘对应的物理磁盘
                    partitions = self.m_wmi.Win32_LogicalDiskToPartition()
                    for partition_rel in partitions:
                        if partition_rel.Dependent.DeviceID == "C:":
                            partition = partition_rel.Antecedent
                            disk_drives = self.m_wmi.Win32_DiskDriveToDiskPartition()
                            for drive_rel in disk_drives:
                                if drive_rel.Dependent.DeviceID == partition.DeviceID:
                                    system_disk_index = drive_rel.Antecedent.Index
                                    break
                            break
                    break

            # 获取物理磁盘信息
            disk_info = self.m_wmi.Win32_DiskDrive()
            for disk in disk_info:
                if system_disk_index is not None and disk.Index == system_disk_index:
                    if disk.SerialNumber:
                        return disk.SerialNumber.strip()

            # 如果无法获取系统盘序列号，使用第一个有序列号的磁盘
            for disk in disk_info:
                if disk.SerialNumber:
                    return disk.SerialNumber.strip()

            return "DEFAULT_SERIAL"
        except Exception as e:
            print(f"获取磁盘序列号错误: {str(e)}")
            return "DEFAULT_SERIAL"

    def get_stable_mac_address(self):
        """获取最稳定的MAC地址"""
        try:
            network_configs = self.m_wmi.Win32_NetworkAdapterConfiguration()

            # 收集所有有效的MAC地址，按优先级排序
            mac_candidates = []

            for network in network_configs:
                if network.MacAddress and network.MacAddress != "00:00:00:00:00:00":
                    mac_info = {
                        'mac': network.MacAddress.replace(":", ""),
                        'description': network.Description or "",
                        'ip_enabled': network.IPEnabled,
                        'physical': 'Virtual' not in (network.Description or "")
                    }
                    mac_candidates.append(mac_info)

            # 排序优先级：物理网卡 > 启用IP > 描述不包含Virtual
            mac_candidates.sort(key=lambda x: (
                x['physical'],      # 物理网卡优先
                x['ip_enabled'],    # 启用IP的优先
                'Ethernet' in x['description'],  # 以太网优先
                x['mac']           # MAC地址字典序
            ), reverse=True)

            if mac_candidates:
                selected_mac = mac_candidates[0]['mac']
                print(f"选择MAC地址: {selected_mac} ({mac_candidates[0]['description']})")
                return selected_mac

            return "ABCDEF123456"
        except Exception as e:
            print(f"获取MAC地址错误: {e}")
            return "ABCDEF123456"

    def get_board_serial(self):
        """获取主板序列号(14位)"""
        try:
            board_info = self.m_wmi.Win32_BaseBoard()
            if len(board_info) > 0 and board_info[0].SerialNumber:
                serial = board_info[0].SerialNumber.strip().strip('.')
                # 过滤掉无效的序列号
                if serial and serial not in ["Default string", "To be filled by O.E.M.", "None"]:
                    return serial
            return "ABCDEFGHIJKLMN"
        except Exception as e:
            print(f"获取主板序列号错误: {e}")
            return "ABCDEFGHIJKLMN"

    def get_system_uuid(self):
        """获取系统UUID作为额外的稳定标识"""
        try:
            computer_system = self.m_wmi.Win32_ComputerSystemProduct()
            if len(computer_system) > 0 and computer_system[0].UUID:
                system_uuid = computer_system[0].UUID.strip()
                if system_uuid and system_uuid != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF":
                    return system_uuid.replace("-", "")[:16]  # 取前16位
            return "DEFAULT_UUID_16"
        except Exception as e:
            print(f"获取系统UUID错误: {e}")
            return "DEFAULT_UUID_16"

    def get_hardware_components(self, use_cache=True):
        """获取硬件组件信息，支持缓存"""
        if use_cache and self._cached_components:
            return self._cached_components

        if use_cache:
            cached = self._load_cached_components()
            if cached:
                self._cached_components = cached
                return cached

        # 获取硬件信息
        components = {
            'cpu_serial': self.get_cpu_serial(),
            'disk_serial': self.get_disk_serial(),
            'mac_address': self.get_stable_mac_address(),
            'board_serial': self.get_board_serial(),
            'system_uuid': self.get_system_uuid(),
            'timestamp': str(int(os.path.getctime(__file__)))  # 使用文件创建时间作为版本标识
        }

        self._cached_components = components
        if use_cache:
            self._save_cached_components(components)

        return components

    def get_machine_code(self, use_cache=True):
        """生成稳定的机器码"""
        try:
            components = self.get_hardware_components(use_cache)

            # 使用多个硬件特征组合，提高稳定性
            combine_str = (
                self.pre_str +
                components['mac_address'] +
                components['cpu_serial'] +
                components['disk_serial'] +
                components['board_serial'] +
                components['system_uuid'] +
                self.suf_str
            )

            # 使用SHA-256替代MD5，提高安全性
            machine_code = hashlib.sha256(combine_str.encode("utf-8")).hexdigest().upper()
            return machine_code

        except Exception as e:
            print(f"生成机器码错误: {e}")
            # 如果出错，尝试使用缓存的组件
            if use_cache:
                try:
                    return self.get_machine_code(use_cache=False)
                except:
                    pass

            # 最后的备用方案
            fallback_str = self.pre_str + "FALLBACK_MACHINE_CODE" + self.suf_str
            return hashlib.sha256(fallback_str.encode("utf-8")).hexdigest().upper()

    def clear_cache(self):
        """清除缓存，强制重新获取硬件信息"""
        self._cached_components = None
        try:
            if os.path.exists(self._cache_file):
                os.remove(self._cache_file)
                print("机器码缓存已清除")
        except Exception as e:
            print(f"清除缓存失败: {e}")

    def validate_machine_code_stability(self, iterations=3):
        """验证机器码稳定性"""
        codes = []
        for i in range(iterations):
            # 每次都重新获取（不使用缓存）
            code = self.get_machine_code(use_cache=False)
            codes.append(code)

        unique_codes = set(codes)
        is_stable = len(unique_codes) == 1

        if not is_stable:
            print(f"警告：机器码不稳定，发现 {len(unique_codes)} 个不同的机器码")
            for i, code in enumerate(codes):
                print(f"  第 {i+1} 次: {code}")

        return is_stable, codes[0] if codes else None