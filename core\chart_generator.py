#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
违章数据图表生成模块
用于生成违章统计图表并嵌入邮件
"""

import os
import tempfile
from datetime import datetime
import base64
from io import BytesIO

try:
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    from matplotlib import rcParams
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，图表功能将不可用")


class ViolationChartGenerator:
    """违章数据图表生成器"""
    
    def __init__(self):
        """初始化图表生成器"""
        self.temp_dir = tempfile.gettempdir()
        
        if MATPLOTLIB_AVAILABLE:
            # 设置中文字体
            self._setup_chinese_font()
    
    def _setup_chinese_font(self):
        """设置中文字体"""
        try:
            # 尝试设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
        except Exception as e:
            print(f"设置中文字体失败: {e}")
    
    def generate_violation_summary_chart(self, violations_data, plate):
        """生成违章汇总图表
        
        Args:
            violations_data: 违章数据字典
            plate: 车牌号
            
        Returns:
            tuple: (图表文件路径, base64编码的图表数据)
        """
        if not MATPLOTLIB_AVAILABLE:
            return None, None
        
        try:
            violations = violations_data.get('violations', [])
            if not violations:
                return None, None
            
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'车牌 {plate} 违章统计报告', fontsize=16, fontweight='bold')
            
            # 1. 违章类型分布饼图
            violation_types = {}
            for v in violations:
                vtype = v.get('wfms', '未知违章')[:10] + '...' if len(v.get('wfms', '')) > 10 else v.get('wfms', '未知违章')
                violation_types[vtype] = violation_types.get(vtype, 0) + 1
            
            if violation_types:
                ax1.pie(violation_types.values(), labels=violation_types.keys(), autopct='%1.1f%%')
                ax1.set_title('违章类型分布')
            
            # 2. 罚款金额分布柱状图
            fine_ranges = {'0-100': 0, '100-200': 0, '200-500': 0, '500+': 0}
            for v in violations:
                fine = float(v.get('fkje', 0) or 0)
                if fine <= 100:
                    fine_ranges['0-100'] += 1
                elif fine <= 200:
                    fine_ranges['100-200'] += 1
                elif fine <= 500:
                    fine_ranges['200-500'] += 1
                else:
                    fine_ranges['500+'] += 1
            
            ax2.bar(fine_ranges.keys(), fine_ranges.values(), color=['green', 'yellow', 'orange', 'red'])
            ax2.set_title('罚款金额分布')
            ax2.set_ylabel('违章数量')
            
            # 3. 记分分布柱状图
            point_ranges = {'0分': 0, '1-3分': 0, '4-6分': 0, '7-12分': 0}
            for v in violations:
                points = int(v.get('_points', 0) or 0)
                if points == 0:
                    point_ranges['0分'] += 1
                elif points <= 3:
                    point_ranges['1-3分'] += 1
                elif points <= 6:
                    point_ranges['4-6分'] += 1
                else:
                    point_ranges['7-12分'] += 1
            
            ax3.bar(point_ranges.keys(), point_ranges.values(), color=['blue', 'green', 'orange', 'red'])
            ax3.set_title('记分分布')
            ax3.set_ylabel('违章数量')
            
            # 4. 处理状态分布饼图
            status_count = {'未处理': 0, '已处理未缴费': 0, '已处理已缴费': 0}
            for v in violations:
                clbj = v.get('clbj', '')
                jkbj = v.get('jkbj', '')
                if clbj == "1" and jkbj == "1":
                    status_count['已处理已缴费'] += 1
                elif clbj == "1":
                    status_count['已处理未缴费'] += 1
                else:
                    status_count['未处理'] += 1
            
            colors = ['red', 'orange', 'green']
            ax4.pie(status_count.values(), labels=status_count.keys(), autopct='%1.1f%%', colors=colors)
            ax4.set_title('处理状态分布')
            
            plt.tight_layout()
            
            # 保存图表
            chart_path = os.path.join(self.temp_dir, f"violation_chart_{plate}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            
            # 生成base64编码
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            plt.close()
            
            return chart_path, chart_base64
            
        except Exception as e:
            print(f"生成图表失败: {e}")
            return None, None
    
    def generate_monthly_trend_chart(self, violations_data, plate):
        """生成月度违章趋势图表
        
        Args:
            violations_data: 违章数据字典
            plate: 车牌号
            
        Returns:
            tuple: (图表文件路径, base64编码的图表数据)
        """
        if not MATPLOTLIB_AVAILABLE:
            return None, None
        
        try:
            violations = violations_data.get('violations', [])
            if not violations:
                return None, None
            
            # 按月份统计违章数量
            monthly_count = {}
            for v in violations:
                date_str = v.get('wfsj', '')
                if date_str:
                    try:
                        # 提取年月
                        if len(date_str) >= 7:
                            month_key = date_str[:7]  # YYYY-MM
                            monthly_count[month_key] = monthly_count.get(month_key, 0) + 1
                    except:
                        continue
            
            if not monthly_count:
                return None, None
            
            # 排序月份
            sorted_months = sorted(monthly_count.keys())
            counts = [monthly_count[month] for month in sorted_months]
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.plot(sorted_months, counts, marker='o', linewidth=2, markersize=8)
            ax.set_title(f'车牌 {plate} 月度违章趋势', fontsize=14, fontweight='bold')
            ax.set_xlabel('月份')
            ax.set_ylabel('违章数量')
            ax.grid(True, alpha=0.3)
            
            # 旋转x轴标签
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # 保存图表
            chart_path = os.path.join(self.temp_dir, f"trend_chart_{plate}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            
            # 生成base64编码
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            plt.close()
            
            return chart_path, chart_base64
            
        except Exception as e:
            print(f"生成趋势图表失败: {e}")
            return None, None
    
    def cleanup_temp_files(self, file_paths):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"清理临时文件失败: {e}")


def install_matplotlib():
    """安装matplotlib的辅助函数"""
    try:
        import subprocess
        import sys
        
        print("正在安装matplotlib...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "matplotlib"])
        print("matplotlib安装成功！")
        return True
    except Exception as e:
        print(f"matplotlib安装失败: {e}")
        return False
