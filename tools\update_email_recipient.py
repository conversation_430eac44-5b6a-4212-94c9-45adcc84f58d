#!/usr/bin/env python3
"""
邮箱收件人修改工具

用于修改软件中的收件人邮箱地址，支持加密和明文配置文件
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.email_config import EmailConfigManager
from encryption.aes_crypto import AESCrypto
from encryption.config import AUTH_CONFIG

def update_recipient_email(old_email, new_email):
    """更新收件人邮箱地址"""
    print("=" * 60)
    print("邮箱收件人修改工具")
    print("=" * 60)
    print(f"将收件人邮箱从 {old_email} 修改为 {new_email}")
    print()
    
    # 1. 处理主配置文件
    print("1. 处理主配置文件...")
    config_manager = EmailConfigManager()
    
    try:
        # 加载当前配置
        config, msg = config_manager.load_config()
        print(f"   加载配置: {msg}")
        
        if config:
            print(f"   当前收件人列表: {config.get('recipients', [])}")
            
            # 更新收件人列表
            recipients = config.get('recipients', [])
            updated = False
            
            # 替换旧邮箱为新邮箱
            for i, recipient in enumerate(recipients):
                if recipient == old_email:
                    recipients[i] = new_email
                    updated = True
                    print(f"   ✅ 已更新收件人: {old_email} -> {new_email}")
            
            # 如果没找到旧邮箱，检查是否需要添加新邮箱
            if not updated:
                if new_email not in recipients:
                    recipients.append(new_email)
                    print(f"   ✅ 已添加新收件人: {new_email}")
                    updated = True
                else:
                    print(f"   ℹ️  新邮箱 {new_email} 已存在于收件人列表中")
            
            if updated:
                config['recipients'] = recipients
                
                # 保存配置（加密保存）
                success, save_msg = config_manager.save_config(config, encrypt=True)
                if success:
                    print(f"   ✅ 配置保存成功: {save_msg}")
                else:
                    print(f"   ❌ 配置保存失败: {save_msg}")
            else:
                print("   ℹ️  无需更新主配置文件")
        else:
            print("   ❌ 无法加载配置文件")
    
    except Exception as e:
        print(f"   ❌ 处理主配置文件时出错: {e}")
    
    print()
    
    # 2. 处理其他可能的配置文件
    print("2. 检查其他配置文件...")
    
    config_files = [
        "config/email_config.json",
        "config/email_config.dat", 
        "config/email_config.enc",
        "config/config.enc",
        "config/config_encrypted.dat"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"   检查文件: {config_file}")
            try:
                if config_file.endswith('.json'):
                    # 明文JSON文件
                    with open(config_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if 'recipients' in data:
                        recipients = data['recipients']
                        updated = False
                        
                        for i, recipient in enumerate(recipients):
                            if recipient == old_email:
                                recipients[i] = new_email
                                updated = True
                        
                        if not updated and new_email not in recipients:
                            recipients.append(new_email)
                            updated = True
                        
                        if updated:
                            data['recipients'] = recipients
                            with open(config_file, 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=4)
                            print(f"     ✅ 已更新 {config_file}")
                        else:
                            print(f"     ℹ️  {config_file} 无需更新")
                    else:
                        print(f"     ℹ️  {config_file} 不包含收件人信息")
                
                elif config_file.endswith(('.dat', '.enc')):
                    # 加密文件
                    try:
                        aes_key = AUTH_CONFIG["aes_key"].encode("utf-8")
                        aes_iv = AUTH_CONFIG["aes_iv"].encode("utf-8")
                        crypto = AESCrypto(aes_key, aes_iv)
                        
                        with open(config_file, 'r', encoding='utf-8') as f:
                            encrypted_data = f.read().strip()
                        
                        decrypted_json = crypto.decrypt_from_base64(encrypted_data)
                        data = json.loads(decrypted_json)
                        
                        # 移除校验信息
                        data.pop('_config_version', None)
                        data.pop('_save_time', None)
                        data.pop('_checksum', None)
                        
                        if 'recipients' in data:
                            recipients = data['recipients']
                            updated = False
                            
                            for i, recipient in enumerate(recipients):
                                if recipient == old_email:
                                    recipients[i] = new_email
                                    updated = True
                            
                            if not updated and new_email not in recipients:
                                recipients.append(new_email)
                                updated = True
                            
                            if updated:
                                data['recipients'] = recipients
                                
                                # 重新加密保存
                                updated_json = json.dumps(data, ensure_ascii=False, indent=4)
                                encrypted_data = crypto.encrypt_to_base64(updated_json)
                                
                                with open(config_file, 'w', encoding='utf-8') as f:
                                    f.write(encrypted_data)
                                print(f"     ✅ 已更新加密文件 {config_file}")
                            else:
                                print(f"     ℹ️  加密文件 {config_file} 无需更新")
                        else:
                            print(f"     ℹ️  加密文件 {config_file} 不包含收件人信息")
                    
                    except Exception as decrypt_error:
                        print(f"     ⚠️  无法解密 {config_file}: {decrypt_error}")
            
            except Exception as e:
                print(f"     ❌ 处理 {config_file} 时出错: {e}")
        else:
            print(f"   跳过不存在的文件: {config_file}")
    
    print()
    print("=" * 60)
    print("修改完成！")
    print("=" * 60)
    print("📋 修改总结:")
    print(f"   • 旧邮箱: {old_email}")
    print(f"   • 新邮箱: {new_email}")
    print("\n⚠️  重要提醒:")
    print("   • 请重启软件以使更改生效")
    print("   • 建议在邮件设置中验证收件人列表")
    print("   • 如有问题，请检查软件的邮件配置页面")

def main():
    """主函数"""
    print("邮箱收件人修改工具")
    print("=" * 40)
    
    if len(sys.argv) == 3:
        old_email = sys.argv[1]
        new_email = sys.argv[2]
    else:
        # 交互式输入
        old_email = input("请输入要替换的旧邮箱地址: ").strip()
        new_email = input("请输入新的邮箱地址: ").strip()
    
    if not old_email or not new_email:
        print("❌ 邮箱地址不能为空")
        return
    
    if "@" not in old_email or "@" not in new_email:
        print("❌ 请输入有效的邮箱地址")
        return
    
    if old_email == new_email:
        print("❌ 新旧邮箱地址相同，无需修改")
        return
    
    # 确认修改
    print(f"\n确认要将收件人邮箱从 '{old_email}' 修改为 '{new_email}' 吗？")
    confirm = input("请输入 'yes' 确认: ").strip().lower()
    
    if confirm == 'yes':
        update_recipient_email(old_email, new_email)
    else:
        print("修改已取消")

if __name__ == "__main__":
    main()
