#!/usr/bin/env python3
"""
启动速度测试工具

测试机器码生成的启动速度优化效果
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_startup_speed():
    """测试启动速度"""
    print("=" * 60)
    print("启动速度测试工具")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 测试快速模式
    print("1. 测试快速启动模式...")
    try:
        from encryption.fast_machine_code import FastMachineCodeGenerator
        
        # 清除缓存，模拟首次启动
        fast_gen = FastMachineCodeGenerator()
        fast_gen.clear_cache()
        
        start_time = time.time()
        fast_code = fast_gen.get_machine_code(use_cache=False)
        fast_time_no_cache = time.time() - start_time
        
        print(f"   快速模式（无缓存）: {fast_time_no_cache:.2f}秒")
        print(f"   机器码: {fast_code[:32]}...")
        
        # 测试缓存效果
        start_time = time.time()
        fast_code_cached = fast_gen.get_machine_code(use_cache=True)
        fast_time_cached = time.time() - start_time
        
        print(f"   快速模式（有缓存）: {fast_time_cached:.4f}秒")
        
        if fast_code == fast_code_cached:
            print("   ✅ 缓存机器码一致")
        else:
            print("   ❌ 缓存机器码不一致")
            
    except Exception as e:
        print(f"   ❌ 快速模式测试失败: {e}")
        fast_time_no_cache = float('inf')
        fast_time_cached = float('inf')
    
    print()
    
    # 2. 测试标准模式
    print("2. 测试标准模式...")
    try:
        from encryption.machine_code import MachineCodeGenerator
        
        std_gen = MachineCodeGenerator()
        std_gen.clear_cache()
        
        start_time = time.time()
        std_code = std_gen.get_machine_code(use_cache=False, fast_mode=False)
        std_time_no_cache = time.time() - start_time
        
        print(f"   标准模式（无缓存）: {std_time_no_cache:.2f}秒")
        print(f"   机器码: {std_code[:32]}...")
        
        # 测试缓存效果
        start_time = time.time()
        std_code_cached = std_gen.get_machine_code(use_cache=True, fast_mode=False)
        std_time_cached = time.time() - start_time
        
        print(f"   标准模式（有缓存）: {std_time_cached:.4f}秒")
        
        if std_code == std_code_cached:
            print("   ✅ 缓存机器码一致")
        else:
            print("   ❌ 缓存机器码不一致")
            
    except Exception as e:
        print(f"   ❌ 标准模式测试失败: {e}")
        std_time_no_cache = float('inf')
        std_time_cached = float('inf')
    
    print()
    
    # 3. 测试AuthManager启动速度
    print("3. 测试AuthManager启动速度...")
    try:
        # 测试快速启动
        start_time = time.time()
        from encryption.auth_manager import AuthManager
        auth_manager = AuthManager()
        auth_time = time.time() - start_time
        
        print(f"   AuthManager启动时间: {auth_time:.2f}秒")
        print(f"   生成的机器码: {auth_manager.get_machine_code()[:32]}...")
        
    except Exception as e:
        print(f"   ❌ AuthManager测试失败: {e}")
        auth_time = float('inf')
    
    print()
    
    # 4. 性能对比和建议
    print("=" * 60)
    print("性能对比和建议")
    print("=" * 60)
    
    print("📊 性能对比:")
    if fast_time_no_cache != float('inf'):
        print(f"   • 快速模式（首次）: {fast_time_no_cache:.2f}秒")
    if std_time_no_cache != float('inf'):
        print(f"   • 标准模式（首次）: {std_time_no_cache:.2f}秒")
    if auth_time != float('inf'):
        print(f"   • AuthManager启动: {auth_time:.2f}秒")
    
    print(f"\n📈 优化效果:")
    if fast_time_no_cache != float('inf') and std_time_no_cache != float('inf'):
        if fast_time_no_cache < std_time_no_cache:
            speedup = std_time_no_cache / fast_time_no_cache
            print(f"   ✅ 快速模式比标准模式快 {speedup:.1f}x")
        else:
            print("   ⚠️  快速模式未显示明显优势")
    
    print(f"\n🎯 启动速度评估:")
    if auth_time < 2:
        print("   ✅ 启动速度优秀（<2秒）")
    elif auth_time < 5:
        print("   ✅ 启动速度良好（<5秒）")
    elif auth_time < 10:
        print("   ⚠️  启动速度一般（<10秒）")
    else:
        print("   ❌ 启动速度较慢（>10秒）")
    
    print(f"\n💡 优化建议:")
    if auth_time > 5:
        print("   • 确保启用快速启动模式")
        print("   • 首次运行后重启软件以使用缓存")
        print("   • 检查网络和磁盘性能")
    elif auth_time > 2:
        print("   • 启动速度已优化，可考虑进一步调整超时设置")
    else:
        print("   • 启动速度已达到最佳状态")
    
    print(f"\n⚙️  当前配置:")
    try:
        from encryption.config import AUTH_CONFIG
        print(f"   • 快速启动: {AUTH_CONFIG.get('fast_startup', True)}")
        print(f"   • 使用缓存: {AUTH_CONFIG.get('use_machine_code_cache', True)}")
        print(f"   • 硬件超时: {AUTH_CONFIG.get('hardware_timeout', 8)}秒")
        print(f"   • 启动验证: {AUTH_CONFIG.get('machine_code_validation', False)}")
    except:
        print("   • 无法读取配置信息")

def main():
    """主函数"""
    try:
        test_startup_speed()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
