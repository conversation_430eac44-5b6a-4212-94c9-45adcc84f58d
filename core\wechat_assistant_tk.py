import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime, date
import win32gui
import win32con
import win32api
import win32clipboard
import time
import uiautomation as auto
from typing import Dict, List, Optional, Callable, Any
from tkcalendar import DateEntry # <-- 新增导入
import traceback

# 新增 VariableSelectionDialog 类
class VariableSelectionDialog(tk.Toplevel):
    """变量选择对话框"""
    def __init__(self, parent, title="选择变量", variables_dict=None):
        super().__init__(parent)
        self.transient(parent)
        self.title(title)
        self.parent = parent
        self.result = None
        self.variables_dict = variables_dict if variables_dict else {}

        self.geometry("300x250") # Adjust size as needed

        ttk.Label(self, text="请选择要插入的变量:").pack(pady=(10,5), side=tk.TOP)

        # --- Button frame first, at the bottom ---
        button_frame = ttk.Frame(self)
        # Pack this frame at the bottom of the dialog, make it fill horizontally
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5,10), padx=10)

        # --- Buttons within the button_frame (packed from the right) ---
        # This creates [Cancel] [OK] from left to right, if button_frame is wide enough and buttons are right-aligned
        # To get typical [OK] [Cancel] or [Cancel] [OK] aligned to one side:
        ok_button = ttk.Button(button_frame, text="确定", command=self._on_ok)
        ok_button.pack(side=tk.RIGHT, padx=(0, 5), pady=5) # OK on the far right of the button_frame

        cancel_button = ttk.Button(button_frame, text="取消", command=self._on_cancel)
        cancel_button.pack(side=tk.RIGHT, padx=(5, 5), pady=5)   # Cancel to the left of OK
        
        # --- Listbox fills the remaining space ---
        self.listbox = tk.Listbox(self, selectmode=tk.SINGLE, exportselection=False)
        for desc in self.variables_dict.keys():
            self.listbox.insert(tk.END, desc)
        
        if self.variables_dict:
            self.listbox.selection_set(0) # Pre-select the first item

        # Pack listbox to fill the space between label and button_frame
        self.listbox.pack(side=tk.TOP, pady=5, padx=10, fill=tk.BOTH, expand=True)

        # self.protocol("WM_DELETE_WINDOW", self._on_cancel) # Already present
        # self.grab_set() # Modal # Already present
        # self.wait_window(self) # Already present

    def _on_ok(self):
        selected_indices = self.listbox.curselection()
        if selected_indices:
            selected_desc = self.listbox.get(selected_indices[0])
            self.result = self.variables_dict.get(selected_desc)
        self.destroy()

    def _on_cancel(self):
        self.result = None
        self.destroy()

class ViolationDatabase:
    """违章数据库类"""
    def __init__(self, db_path):
        self.db_path = db_path
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """确保数据库文件存在并使用正确的字段结构"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        db_was_missing = not os.path.exists(self.db_path)

        if db_was_missing:
            # 创建数据库和表（与DataManager保持一致的字段结构）
            self._create_correct_table_structure()
        else:
            # 检查现有表结构是否正确
            self._check_and_fix_table_structure()

    def _create_correct_table_structure(self):
        """创建正确的表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS traffic_violations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    hphm TEXT NOT NULL,
                    wfsj DATETIME NOT NULL,
                    wfdz TEXT NOT NULL,
                    wfms TEXT NOT NULL,
                    fkje REAL NOT NULL,
                    _points INTEGER NOT NULL,
                    clbj TEXT NOT NULL,
                    jkbj TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            # 创建索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_hphm
                ON traffic_violations(hphm)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_wfsj
                ON traffic_violations(wfsj)
            ''')
            conn.commit()

    def _check_and_fix_table_structure(self):
        """检查并修复表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='traffic_violations'")
                table_exists = cursor.fetchone() is not None

                if not table_exists:
                    # 表不存在，创建正确的表结构
                    self._create_correct_table_structure()
                    return

                # 检查表结构
                cursor.execute("PRAGMA table_info(traffic_violations)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]

                # 检查是否是错误的字段结构
                old_fields = ['plate_number', 'violation_time', 'violation_location', 'violation_content']
                has_old_fields = any(field in column_names for field in old_fields)

                # 检查是否是正确的字段结构
                new_fields = ['hphm', 'wfsj', 'wfdz', 'wfms']
                has_new_fields = all(field in column_names for field in new_fields)

                if has_new_fields:
                    # 字段结构正确，无需修复
                    return

                if has_old_fields:
                    # 检查是否有数据
                    cursor.execute("SELECT COUNT(*) FROM traffic_violations")
                    data_count = cursor.fetchone()[0]

                    if data_count == 0:
                        # 没有数据，直接重建表结构
                        cursor.execute("DROP TABLE traffic_violations")
                        conn.commit()
                        self._create_correct_table_structure()
                    else:
                        # 有数据，需要迁移（这里只是警告，实际迁移需要用户手动执行）
                        print(f"警告: 数据库表结构需要更新，但表中有 {data_count} 条数据")
                        print("请运行 python fix_db_fields.py 或 python utils/db_migration.py 进行修复")

        except Exception as e:
            print(f"检查表结构时出错: {str(e)}")
            # 出错时尝试创建正确的表结构
            try:
                self._create_correct_table_structure()
            except:
                pass
    
    def get_violations(self, plate_number=None, start_date=None, end_date=None):
        """获取违章记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                where_clauses = []
                params = []
                
                # 使用正确的字段名进行查询
                if plate_number:
                    where_clauses.append("hphm = ?")
                    params.append(plate_number)

                if start_date:
                    where_clauses.append("wfsj >= ?")
                    params.append(start_date)

                if end_date:
                    where_clauses.append("wfsj <= ?")
                    params.append(end_date)

                where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"

                # 查询数据并映射为微信助手期望的字段名
                sql_query = f"""
                    SELECT
                        hphm as plate_number,
                        wfsj as violation_time,
                        wfdz as violation_location,
                        wfms as violation_content,
                        fkje as fine_amount,
                        _points as points,
                        clbj as process_flag,
                        jkbj as payment_flag
                    FROM traffic_violations
                    WHERE {where_clause}
                    ORDER BY wfsj DESC
                """
                cursor.execute(sql_query, params)
                
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                
                # 将结果转换为字典列表
                violations = []
                for row in cursor.fetchall():
                    violation = {}
                    for i, col in enumerate(columns):
                        violation[col] = row[i]
                    violations.append(violation)
                
                return violations
                
        except Exception as e:
            print(f"获取违章记录失败: {str(e)}")
            return []

class WeChatAssistant:
    """微信助手类 - 处理与微信的交互"""
    def __init__(self):
        self.window_class = "WeChatMainWndForPC"
        self.window_name = "微信"
        self.use_ctrl_enter = False  # 默认使用 Enter 发送
    
    def set_clipboard(self, text: str):
        """设置剪贴板内容"""
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        win32clipboard.SetClipboardText(text, win32clipboard.CF_UNICODETEXT)
        win32clipboard.CloseClipboard()

    def switch_to_wechat(self) -> Optional[auto.WindowControl]:
        """切换到微信窗口并最大化"""
        try:
            wechat_win = auto.WindowControl(Name=self.window_name, ClassName=self.window_class)
            
            if not wechat_win.Exists():
                # 发送 Ctrl+Alt+W 快捷键
                win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
                win32api.keybd_event(win32con.VK_MENU, 0, 0, 0)
                win32api.keybd_event(ord('W'), 0, 0, 0)
                time.sleep(0.1)
                win32api.keybd_event(ord('W'), 0, win32con.KEYEVENTF_KEYUP, 0)
                win32api.keybd_event(win32con.VK_MENU, 0, win32con.KEYEVENTF_KEYUP, 0)
                win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
                
                time.sleep(1)
                wechat_win = auto.WindowControl(Name=self.window_name, ClassName=self.window_class)
                
                if not wechat_win.Exists():
                    return None
                
            hwnd = win32gui.FindWindow(self.window_class, self.window_name)
            if hwnd:
                if win32gui.IsIconic(hwnd):
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                win32gui.SetForegroundWindow(hwnd)
                # win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE) # 注释掉此行以取消最大化
                
                time.sleep(0.5)
                return wechat_win
                
            return None
            
        except Exception as e:
            return None

    def send_message(self, contact: Dict[str, str], message: str, 
                    callback: Optional[Callable[[str], None]] = None) -> bool:
        """发送消息给指定联系人"""
        try:
            wechat_win = self.switch_to_wechat()
            if not wechat_win:
                if callback:
                    callback(f"❌ 发送给 {contact['nickname']} 失败：无法切换到微信窗口")
                return False
            
            # 设置剪贴板内容
            self.set_clipboard(message)
            
            # 查找搜索框
            search = wechat_win.EditControl(Name="搜索")
            if not search.Exists():
                if callback:
                    callback(f"❌ 发送给 {contact['nickname']} 失败：未找到搜索框")
                return False
                
            search.Click()
            time.sleep(0.5)
            
            # 清空搜索框
            search.SendKeys('{Ctrl}a')
            time.sleep(0.1)
            search.SendKeys('{Delete}')
            time.sleep(0.1)
            
            # 输入搜索内容并按回车
            # 使用remark作为搜索关键字，如果remark为空则使用nickname
            search_name = contact['remark'] if contact.get('remark') else contact['nickname']
            if callback:
                callback(f"🔍 正在搜索联系人：{search_name}")
            search.SendKeys(search_name, waitTime=0.1)
            time.sleep(1)
            search.SendKeys('{Enter}')
            time.sleep(1)
            
            # 查找联系人列表项
            contact_list = wechat_win.ListControl(Name="会话")
            if contact_list.Exists():
                # 遍历列表项查找匹配的联系人
                items = contact_list.GetChildren()
                found = False
                for item in items:
                    try:
                        name = item.Name
                        # 移除"已置顶"标记再比较
                        if "已置顶" in name:
                            name = name.replace("已置顶", "").strip()
                        
                        # 如果搜索的是车牌号，则判断列表项名称中是否包含车牌号
                        if len(search_name) <= 8 and len(search_name) >= 5:  # 可能是车牌号
                            if search_name.upper() in name.upper():  # 忽略大小写比较
                                item.Click()
                                time.sleep(0.5)
                                found = True
                                break
                        # 否则精确匹配联系人名称
                        elif name == search_name:
                            item.Click()
                            time.sleep(0.5)
                            found = True
                            break
                    except Exception:
                        continue

                if found:
                    # 直接发送消息
                    win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
                    win32api.keybd_event(ord('V'), 0, 0, 0)
                    win32api.keybd_event(ord('V'), 0, win32con.KEYEVENTF_KEYUP, 0)
                    win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
                    time.sleep(0.5)
                    
                    # 发送消息
                    if self.use_ctrl_enter:
                        win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
                        win32api.keybd_event(win32con.VK_RETURN, 0, 0, 0)
                        win32api.keybd_event(win32con.VK_RETURN, 0, win32con.KEYEVENTF_KEYUP, 0)
                        win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
                    else:
                        win32api.keybd_event(win32con.VK_RETURN, 0, 0, 0)
                        win32api.keybd_event(win32con.VK_RETURN, 0, win32con.KEYEVENTF_KEYUP, 0)
                    
                    time.sleep(0.5)
                    
                    # 任务完成
                    if callback:
                        callback(f"✅ 已发送给 {contact['nickname']}")
                    return True
                else:
                    if callback:
                        callback(f"❌ 发送给 {contact['nickname']} 失败：未找到联系人")
                    return False
            
        except Exception as e:
            if callback:
                callback(f"❌ 发送给 {contact['nickname']} 失败：{str(e)}")
            return False

class ViolationDetailDialog:
    """违章明细对话框"""
    def __init__(self, parent, plate_number, violation_db):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"违章明细 - {plate_number}")
        self.dialog.geometry("950x600")  # Increased width from 800 to 900
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.plate_number = plate_number
        self.violation_db = violation_db
        
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化界面"""
        # 创建表格
        columns = ("time", "location", "content", "fine", "points", 
                  "points_status", "payment_status", "total_status")
        self.tree = ttk.Treeview(self.dialog, columns=columns, show="headings")
        
        # 设置列标题
        self.tree.heading("time", text="违章时间")
        self.tree.heading("location", text="违章地点")
        self.tree.heading("content", text="违章内容")
        self.tree.heading("fine", text="罚款")
        self.tree.heading("points", text="记分")
        self.tree.heading("points_status", text="记分处理")
        self.tree.heading("payment_status", text="缴款处理")
        self.tree.heading("total_status", text="总状态")
        
        # 设置列宽
        self.tree.column("time", width=160, anchor="center")
        self.tree.column("location", width=200, anchor="center")
        self.tree.column("content", width=200, anchor="center")
        self.tree.column("fine", width=60, anchor="center")
        self.tree.column("points", width=60, anchor="center")
        self.tree.column("points_status", width=80, anchor="center")
        self.tree.column("payment_status", width=80, anchor="center")
        self.tree.column("total_status", width=80, anchor="center")
        
        # 添加滚动条
        # scrollbar = ttk.Scrollbar(self.dialog, orient=tk.VERTICAL, command=self.tree.yview)
        # self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 统计信息标签
        self.stats_frame = ttk.Frame(self.dialog)
        self.total_label = ttk.Label(self.stats_frame, text="")
        self.fine_label = ttk.Label(self.stats_frame, text="")
        self.points_label = ttk.Label(self.stats_frame, text="")
        self.status_label = ttk.Label(self.stats_frame, text="")
        
        self.total_label.pack(side=tk.LEFT, padx=5)
        self.fine_label.pack(side=tk.LEFT, padx=5)
        self.points_label.pack(side=tk.LEFT, padx=5)
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 关闭按钮
        self.button_frame = ttk.Frame(self.dialog)
        close_btn = ttk.Button(self.button_frame, text="关闭", command=self.dialog.destroy)
        close_btn.pack(padx=5, pady=5)
        
        # 打包所有组件
        self.stats_frame.pack(fill=tk.X, padx=5, pady=5)
        self.tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        # scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.button_frame.pack(fill=tk.X)
    
    def load_data(self):
        """加载违章数据"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 从数据库加载数据
            violations = self.violation_db.get_violations(self.plate_number)
            
            # 统计信息
            total_count = len(violations)
            total_fine = sum(float(v['fine_amount'] or 0) for v in violations)
            total_points = sum(int(v['points'] or 0) for v in violations)
            pending_count = sum(1 for v in violations if v['process_flag'] != '1' or v['payment_flag'] != '1')
            completed_count = total_count - pending_count
            
            # 更新统计标签
            self.total_label.config(text=f"总违章次数: {total_count}次")
            self.fine_label.config(text=f"总罚款金额: {total_fine}元")
            self.points_label.config(text=f"总记分: {total_points}分")
            self.status_label.config(text=f"已处理: {completed_count}次 | 未处理: {pending_count}次")
            
            # 添加数据到表格
            for v in violations:
                # 确定状态
                if v['process_flag'] == '1' and v['payment_flag'] == '1':
                    points_status = "已处理"
                    payment_status = "已缴款" if float(v['fine_amount'] or 0) > 0 else "无需缴款"
                    total_status = "已处理"
                elif v['process_flag'] == '0' and v['payment_flag'] == '0':
                    points_status = "未处理"
                    payment_status = "未缴款" if float(v['fine_amount'] or 0) > 0 else "无需缴款"
                    total_status = "未处理"
                else:
                    points_status = "已处理" if v['process_flag'] == '1' else "未处理"
                    payment_status = "已缴款" if v['payment_flag'] == '1' else "未缴款"
                    total_status = "处理中"
                
                self.tree.insert("", tk.END, values=(
                    v['violation_time'], v['violation_location'], v['violation_content'], 
                    f"{v['fine_amount']}元", f"{v['points']}分",
                    points_status, payment_status, total_status
                ))
                    
        except Exception as e:
            messagebox.showerror("错误", f"加载违章数据失败：{str(e)}")

class WechatAssistantTk(tk.Frame):
    def __init__(self, parent, config_manager, data_manager, auth_manager):
        """初始化微信助手界面
        
        Args:
            parent: 父组件
            config_manager: 配置管理器
            data_manager: 数据管理器
            auth_manager: 授权管理器
        """
        super().__init__(parent)
        self.parent = parent
        self.config_manager = config_manager
        self.data_manager = data_manager
        self.auth_manager = auth_manager
        
        # 初始化核心属性
        # 构建数据库路径，兼容开发环境和打包环境
        db_file_path = self._get_database_path()
        self.db = ViolationDatabase(db_file_path)
        
        self.wechat = WeChatAssistant()
        # 0 for Enter, 1 for Ctrl+Enter
        self.send_key_var = tk.IntVar(value=1 if self.wechat.use_ctrl_enter else 0)
        self.send_key_var.trace_add('write', self._update_send_key_preference)
        
        # plate_violations_map 必须在这里初始化
        self.plate_violations_map: Dict[str, Dict[str, Any]] = {} 
        
        self.current_template_name = tk.StringVar()
        self.message_preview_var = tk.StringVar() # 似乎未直接使用，但保持定义
        self.status_var = tk.StringVar(value="就绪") # 用于内部状态更新，区别于app.py的状态栏

        # 加载提示语模板 - templates 属性由这个方法返回并赋值
        self.templates: Dict[str, List[Dict[str, str]]] = {}
        self.current_selected_template_name = tk.StringVar()
        self.load_templates_and_init_selection()
        self._initial_sash_set = False # Flag for initial sash position
        
        # 初始化UI组件 - init_ui 可能会用到上面定义的属性 (如 self.status_var)
        # init_ui 也会创建 self.tree, self.tips_text, self.preview_text, self.status_text 等UI元素
        self.init_ui()
        
        # 不应在 __init__ 中直接调用 load_data()
        # self.load_data() # 由 app.py 控制加载时机

    def _get_database_path(self):
        """获取数据库文件路径，兼容开发环境和打包环境"""
        # 使用与DataManager相同的路径获取逻辑
        from core.data_manager import get_database_path
        return get_database_path()

    def _get_config_path(self):
        """获取配置文件路径，兼容开发环境和打包环境"""
        import sys

        # 检测是否在 PyInstaller 打包环境中
        if hasattr(sys, '_MEIPASS'):
            # 打包环境：优先使用_internal/config目录
            internal_config_path = os.path.join(sys._MEIPASS, "_internal", "config", "message_templates.json")
            if os.path.exists(internal_config_path):
                return internal_config_path

            # 如果_internal/config不存在，使用用户数据目录
            app_name = "ViolationQueryApp"
            user_config_dir = os.path.join(os.getenv('APPDATA'), app_name, "config")
            os.makedirs(user_config_dir, exist_ok=True)
            config_path = os.path.join(user_config_dir, "message_templates.json")
            return config_path
        else:
            # 开发环境：使用项目相对路径
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(project_root, 'config', 'message_templates.json')
            return config_path

    def _show_unauthorized_message(self): # Helper for auth check, if ever needed locally
        """显示未授权提示"""
        # Clear existing widgets if any
        for widget in self.winfo_children():
            widget.destroy()
        ttk.Label(self, 
                  text="您没有访问微信助手功能的权限。\n请联系管理员获取授权。",
                  font=("Arial", 12),
                  padding=20,
                  anchor=tk.CENTER,
                  justify=tk.CENTER).pack(expand=True, fill=tk.BOTH)

    def _update_send_key_preference(self, *args):
        """更新微信发送快捷键设置"""
        self.wechat.use_ctrl_enter = (self.send_key_var.get() == 1)

    def insert_variable(self):
        """插入车牌号变量到提示语文本框的光标位置"""
        self.tips_text.insert(tk.INSERT, "{plate}")

    def insert_driver(self):
        """插入司机姓名变量到提示语文本框的光标位置"""
        self.tips_text.insert(tk.INSERT, "{driver}")

    def load_templates_and_init_selection(self):
        """加载提示语模板并初始化选择状态"""
        config_path = self._get_config_path()
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 新的默认模板结构
        default_templates_new_format = {
            'violation': [
                {'name': '默认模板1', 'content': "{driver}您好，您的车辆 {plate} 有以下违章记录需要处理："},
                {'name': '温馨提示模板', 'content': "{driver}车主请注意，您的爱车 {plate} 存在未处理违章，详情如下："}
            ]
        }
        
        try:
            if os.path.exists(config_path):
                import json
                with open(config_path, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                    # 检查是否是旧格式（列表）并尝试转换
                    if isinstance(loaded_data.get('violation'), list) and \
                       all(isinstance(t, str) for t in loaded_data['violation']):
                        self.templates['violation'] = [
                            {'name': f'旧模板{i+1}', 'content': content} 
                            for i, content in enumerate(loaded_data['violation'])
                        ]
                        if not self.templates['violation']:
                             self.templates = default_templates_new_format # 如果旧模板转换后为空，使用新的默认
                    elif isinstance(loaded_data.get('violation'), list) and \
                         all(isinstance(t, dict) and 'name' in t and 'content' in t for t in loaded_data['violation']):
                        self.templates = loaded_data # 是新格式
                    else:
                        self.templates = default_templates_new_format # 格式无法识别，使用新的默认
            else:
                self.templates = default_templates_new_format
                with open(config_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(self.templates, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"加载模板失败: {str(e)}")
            self.templates = default_templates_new_format
        
        # 初始化选择和文本框
        violation_templates = self.templates.get('violation', [])
        if violation_templates:
            first_template_name = violation_templates[0]['name']
            first_template_content = violation_templates[0]['content']
            self.current_selected_template_name.set(first_template_name)
            # tips_text 会在 init_ui 中创建，所以这里不能直接 .insert
            # self.initial_tips_content = first_template_content
        else:
            # self.initial_tips_content = ""
            self.current_selected_template_name.set("")

    def load_templates(self): # 旧的 load_templates 实际由 load_templates_and_init_selection 替代
        pass 
        
    def init_ui(self):
        """初始化界面"""
        # 创建主分割窗口
        self.paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        self.paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.paned.bind("<Configure>", self._on_paned_configure)
        
        # 左侧数据显示区域
        self.left_frame = ttk.Frame(self.paned)
        self.paned.add(self.left_frame, weight=6)
        
        # 筛选条件区域
        filter_frame = ttk.LabelFrame(self.left_frame, text="筛选条件")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        filters_controls_row = ttk.Frame(filter_frame)
        filters_controls_row.pack(fill=tk.X, padx=5, pady=(2,5)) # Added some bottom padding

        # 车牌号筛选
        ttk.Label(filters_controls_row, text="车牌号:").pack(side=tk.LEFT)
        self.plate_var = tk.StringVar()
        self.plate_entry = ttk.Entry(filters_controls_row, textvariable=self.plate_var, width=10)
        self.plate_entry.pack(side=tk.LEFT, padx=(0,5))
        self.plate_var.trace_add('write', lambda *args: self.load_data()) 

        # 日期筛选
        ttk.Label(filters_controls_row, text="开始日期:").pack(side=tk.LEFT, padx=(5,0))
        self.start_date_entry = DateEntry(filters_controls_row, width=10, date_pattern='yyyy-mm-dd')
        self.start_date_entry.pack(side=tk.LEFT, padx=(0,5))
        
        ttk.Label(filters_controls_row, text="结束日期:").pack(side=tk.LEFT, padx=(5,0))
        self.end_date_entry = DateEntry(filters_controls_row, width=10, date_pattern='yyyy-mm-dd')
        self.end_date_entry.pack(side=tk.LEFT, padx=(0,5))

        # Set default start date to 2025-01-01
        start_default_date = date(2025, 1, 1)
        self.start_date_entry.set_date(start_default_date)
        
        # Set default end date to today
        self.end_date_entry.set_date(date.today())
        
        self.start_date_entry.bind('<<DateEntrySelected>>', self._filter_data_if_valid_dates)
        self.end_date_entry.bind('<<DateEntrySelected>>', self._filter_data_if_valid_dates)

        # 状态筛选
        ttk.Label(filters_controls_row, text="处理状态:").pack(side=tk.LEFT, padx=(5,0))
        self.status_filter_var = tk.StringVar()
        self.status_filter_combo = ttk.Combobox(filters_controls_row, textvariable=self.status_filter_var, 
                                                values=["全部", "未处理", "已处理"], state='readonly', width=8)
        self.status_filter_combo.pack(side=tk.LEFT, padx=(0,5))
        self.status_filter_combo.set("全部")
        self.status_filter_combo.bind('<<ComboboxSelected>>', lambda event: self.load_data())
        
        # 新增刷新按钮
        self.refresh_button = ttk.Button(filters_controls_row, text="刷新", command=self.load_data)
        self.refresh_button.pack(side=tk.LEFT, padx=(5,0))
        
        # 创建表格
        self.tree = ttk.Treeview(self.left_frame, columns=("select", "plate", "violation_count", 
                                                          "fine_amount", "points", "driver", "unique_id", "status"),
                                show="headings", selectmode="browse")
        
        # 设置列标题和列宽
        columns_config = [
            ("select", "选择", 40, 30), # 新增 minwidth
            ("plate", "车牌号", 100, 80),
            ("violation_count", "违章次数", 80, 60),
            ("fine_amount", "罚款金额", 100, 80),
            ("points", "记分", 80, 50),
            ("driver", "司机", 100, 80),
            ("unique_id", "唯一标识", 100, 80),
            ("status", "状态", 120, 100)
        ]
        
        self.column_configs = columns_config # 保存配置以便后续使用

        for col, heading, width, minwidth in columns_config:
            self.tree.heading(col, text=heading)
            self.tree.column(col, width=width, anchor="center", minwidth=minwidth)
        
        # 设置表格样式
        style = ttk.Style()
        style.configure("Treeview", 
                       background="white",
                       foreground="black",
                       rowheight=25,
                       fieldbackground="white")
        style.configure("Treeview.Heading",
                       background="#f0f0f0",
                       foreground="black",
                       relief="flat")
        style.map("Treeview",
                 background=[("selected", "#e3f2fd")],
                 foreground=[("selected", "black")])
        
        # 设置交替行颜色
        self.tree.tag_configure('oddrow', background='#f5f5f5')
        self.tree.tag_configure('evenrow', background='white')
        
        # 设置状态颜色标签
        self.tree.tag_configure('warning', foreground='#ff4444')
        self.tree.tag_configure('normal', foreground='#4caf50')
        self.tree.tag_configure('pending', foreground='#ff9800')
        
        # 绑定表格事件
        self.tree.bind('<Button-1>', self.on_tree_click)
        self.tree.bind('<Double-1>', self.on_tree_double_click)
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)
        
        # 打包表格
        self.tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 右侧配置区域
        self.right_frame = ttk.Frame(self.paned, width=380) # 初始宽度
        self.paned.add(self.right_frame, weight=3)
        
        # 发送配置区域
        config_frame = ttk.LabelFrame(self.right_frame, text="发送配置")
        config_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 提示语输入区域
        tips_outer_frame = ttk.Frame(config_frame) 
        tips_outer_frame.pack(fill=tk.X, expand=True, padx=5, pady=5) # Fills horizontally

        # --- 模板内容 (选择和编辑) --- 左侧区域,占据剩余空间
        tips_content_labelframe = ttk.LabelFrame(tips_outer_frame, text="消息提示语")
        tips_content_labelframe.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) 
        
        # 模板选择、保存、删除按钮都在这一行 (内部)
        template_actions_frame = ttk.Frame(tips_content_labelframe) 
        template_actions_frame.pack(fill=tk.X, padx=5, pady=(5,0))
        
        # --- 第一行：选择模板 和 插入变量 ---
        template_selection_row_frame = ttk.Frame(template_actions_frame)
        template_selection_row_frame.pack(fill=tk.X)
        ttk.Label(template_selection_row_frame, text="选择模板:").pack(side=tk.LEFT, padx=(0,2))
        template_names = [t['name'] for t in self.templates.get('violation', [])]
        # 调整Combobox宽度以适应插入变量按钮
        self.template_combo = ttk.Combobox(template_selection_row_frame, textvariable=self.current_selected_template_name, 
                                           values=template_names, state='readonly', width=10) 
        self.template_combo.pack(side=tk.LEFT, padx=(0,5))
        self.template_combo.bind('<<ComboboxSelected>>', self.on_template_select)
        ttk.Button(template_selection_row_frame, text="插入车牌号", command=self.insert_variable, width=12).pack(side=tk.LEFT, padx=(5,0))
        ttk.Button(template_selection_row_frame, text="插入司机姓名", command=self.insert_driver, width=12).pack(side=tk.LEFT, padx=(5,0))

        # --- 第二行：保存和删除按钮 (在上一行的下方) ---
        template_buttons_row_frame = ttk.Frame(template_actions_frame)
        template_buttons_row_frame.pack(fill=tk.X, pady=(2,0)) # pady=(top, bottom)
        ttk.Button(template_buttons_row_frame, text="保存模板", command=self.save_template, width=10).pack(side=tk.LEFT, padx=(0,2))
        ttk.Button(template_buttons_row_frame, text="删除模板", command=self.delete_selected_template, width=10).pack(side=tk.LEFT, padx=(2,0))

        # 模板内容 Text (位于 template_actions_frame 下方，保持在 tips_content_labelframe 内)
        self.tips_text = tk.Text(tips_content_labelframe, height=3) # Reduced height slightly 
        self.tips_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5) 
        
        # 初始化 tips_text 内容 (逻辑不变)
        selected_template_name = self.current_selected_template_name.get()
        initial_content = ""
        if selected_template_name:
            for t in self.templates.get('violation', []):
                if t['name'] == selected_template_name:
                    initial_content = t['content']
                    break
        self.tips_text.insert('1.0', initial_content)
        self.tips_text.bind('<KeyRelease>', lambda e: self.update_preview_and_clear_selection_on_edit())
        
        # --- 违章字段选择 --- 
        fields_frame = ttk.LabelFrame(config_frame, text="违章字段选择")
        fields_frame.pack(fill=tk.X, padx=5, pady=5)

        self.field_vars = {}
        violation_fields = {
            "violation_time": "违章时间",
            "violation_location": "违章地点",
            "violation_content": "违章内容",
            "fine_amount": "罚款金额",
            "points": "记分",
            "process_flag": "处理标记",
            "payment_flag": "缴款标记",
            "summary_stats": "统计信息",
            "include_processed": "包含已处理违章" # 新增选项
        }

        num_columns = 3 # 调整为3列，因为增加了一个选项
        for i, key in enumerate(violation_fields.keys()):
            text = violation_fields[key]
            # 默认不选中"包含已处理违章"
            default_value = False if key == "include_processed" else True
            var = tk.BooleanVar(value=default_value)  
            self.field_vars[key] = var
            
            row = i // num_columns
            col = i % num_columns
            
            cb = ttk.Checkbutton(fields_frame, text=text, variable=var, command=self.update_preview)
            cb.grid(row=row, column=col, padx=5, pady=2, sticky=tk.W)

        # Configure columns to have equal weight if you want them to expand
        # For now, let's assume fixed width based on content is fine.
        # If alignment issues persist, uncomment and adjust:
        # for c in range(num_columns):
        #     fields_frame.grid_columnconfigure(c, weight=1)

        # 预览区域
        preview_frame = ttk.LabelFrame(config_frame, text="消息预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.preview_text = tk.Text(preview_frame, height=10)
        self.preview_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 发送状态区域
        status_frame = ttk.LabelFrame(config_frame, text="发送状态")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.status_text = tk.Text(status_frame, height=6)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 操作按钮区域
        action_buttons_outer_frame = ttk.Frame(config_frame)
        action_buttons_outer_frame.pack(fill=tk.X, padx=5, pady=5)

        main_action_buttons_frame = ttk.Frame(action_buttons_outer_frame)
        main_action_buttons_frame.pack(fill=tk.X) # 第一行按钮
        
        ttk.Button(main_action_buttons_frame, text="全选车辆", command=self.toggle_select_all).pack(side=tk.LEFT, padx=2)
        ttk.Button(main_action_buttons_frame, text="发送微信通知", command=self.send_messages).pack(side=tk.LEFT, padx=2)

        # 发送方式 Radiobuttons (第二行)
        send_method_frame = ttk.Frame(action_buttons_outer_frame)
        send_method_frame.pack(fill=tk.X, pady=(2,0))
        ttk.Label(send_method_frame, text="发送方式:").pack(side=tk.LEFT, padx=(0, 2))
        ttk.Radiobutton(send_method_frame, text="Enter", variable=self.send_key_var, value=0).pack(side=tk.LEFT)
        ttk.Radiobutton(send_method_frame, text="Ctrl+Enter", variable=self.send_key_var, value=1).pack(side=tk.LEFT, padx=(0, 5))
        
        # 新增：搜索方式选择 (第三行)
        search_method_frame = ttk.Frame(action_buttons_outer_frame)
        search_method_frame.pack(fill=tk.X, pady=(2,0))
        ttk.Label(search_method_frame, text="搜索方式:").pack(side=tk.LEFT, padx=(0, 2))
        
        # 搜索方式单选变量 (0: 车牌号, 1: 司机, 2: 唯一标识)
        self.search_method_var = tk.IntVar(value=0)  # 默认使用车牌号
        ttk.Radiobutton(search_method_frame, text="车牌号", variable=self.search_method_var, value=0).pack(side=tk.LEFT)
        ttk.Radiobutton(search_method_frame, text="司机", variable=self.search_method_var, value=1).pack(side=tk.LEFT)
        ttk.Radiobutton(search_method_frame, text="唯一标识", variable=self.search_method_var, value=2).pack(side=tk.LEFT)
    
    def load_data(self):
        """从数据库加载所有车辆的违章概览信息并更新Treeview，根据筛选条件"""
        self.update_status("正在从数据库加载数据...")
        
        plate_filter = self.plate_var.get().strip().upper()
        start_date_str = self.start_date_entry.get()
        end_date_str = self.end_date_entry.get()
        status_filter = self.status_filter_var.get()

        start_date = start_date_str if self._is_valid_date_format(start_date_str) and start_date_str else None
        end_date = end_date_str if self._is_valid_date_format(end_date_str) and end_date_str else None

        for item in self.tree.get_children():
            self.tree.delete(item)
        self.plate_violations_map.clear()

        try:
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                query = "SELECT DISTINCT hphm FROM traffic_violations WHERE 1=1"
                params = []
                if plate_filter:
                    query += " AND hphm LIKE ?"
                    params.append(f"%{plate_filter}%")
                cursor.execute(query, params)
                unique_plates = [row[0] for row in cursor.fetchall()]

            if not unique_plates:
                self.update_status("数据库中暂无违章记录，请先进行违章查询。")
                return

            for plate_number in unique_plates:
                violations = self.db.get_violations(plate_number=plate_number, start_date=start_date, end_date=end_date)
                if not violations:
                    continue

                total_count_initial = len(violations)
                pending_count = 0
                latest_violation_time = ""
                total_fine_for_plate = 0
                total_points_for_plate = 0

                for v in violations:
                    clbj = v.get('process_flag', '0')
                    jkbj = v.get('payment_flag', '0')
                    
                    try:
                        fkje_val = int(float(v.get('fine_amount', 0)))
                    except ValueError:
                        fkje_val = 0
                    total_fine_for_plate += fkje_val

                    try:
                        points_val_str = v.get('points', '0')
                        points_val = int(float(points_val_str if points_val_str else '0'))
                    except ValueError:
                        points_val = 0
                    total_points_for_plate += points_val
                    
                    # 简化的未处理判断逻辑（移除'9'状态）
                    try:
                        points_val = int(float(v.get('points', 0)))
                    except (ValueError, TypeError):
                        points_val = 0

                    is_pending = False
                    # 有记分且未处理
                    if points_val > 0 and clbj == '0':
                        is_pending = True
                    # 有罚款且未缴款
                    elif fkje_val > 0 and jkbj == '0':
                        is_pending = True
                    
                    if is_pending:
                        pending_count += 1
                    
                    current_v_time = v.get('violation_time', '')
                    if current_v_time:
                        if not latest_violation_time or current_v_time > latest_violation_time:
                            latest_violation_time = current_v_time
                
                display_this_plate = False
                if status_filter == "全部":
                    display_this_plate = True
                elif status_filter == "未处理":
                    if pending_count > 0:
                        display_this_plate = True
                elif status_filter == "已处理":
                    if total_count_initial > 0 and pending_count == 0:
                        display_this_plate = True
                    elif total_count_initial == 0:
                        display_this_plate = True
                
                if not display_this_plate:
                    continue

                driver_name = "未分配"
                unique_id = "未知"
                vehicle_info = self.data_manager.get_vehicle_info(plate_number)
                if vehicle_info:
                    driver_name = vehicle_info.get('driver_name', "未分配")
                    unique_id = vehicle_info.get('unique_id', "未知")

                self.plate_violations_map[plate_number] = {
                    'violations': violations,
                    'summary': {
                        'total': total_count_initial,
                        'pending': pending_count,
                        'plate_number': plate_number,
                        'total_fine': total_fine_for_plate,
                        'total_points': total_points_for_plate,
                        'latest_violation_time': latest_violation_time,
                        'driver_name': driver_name, # 存储司机姓名
                        'unique_id': unique_id    # 存储唯一标识 (如果需要)
                    },
                    'send_status': "未发送"
                }
                
                # Corrected item_values order based on defined columns
                item_values = (
                    "☐",                       # select
                    plate_number,              # plate
                    total_count_initial,       # violation_count
                    total_fine_for_plate,      # fine_amount
                    total_points_for_plate,    # points
                    driver_name,               # driver
                    unique_id,                 # unique_id
                    self.plate_violations_map[plate_number].get('send_status', "未发送") # status
                )
                self.tree.insert("", tk.END, values=item_values, tags=(plate_number,))
            
            self.update_status(f"成功从数据库加载了 {len(self.tree.get_children())} 个符合条件的车牌数据。")

        except Exception as e:
            self.update_status(f"从数据库加载数据失败: {str(e)}")
            messagebox.showerror("数据库错误", f"加载车辆违章数据时出错: {str(e)}", parent=self.parent)
            print(f"Error in load_data: {e}\n{traceback.format_exc()}")

    def display_query_results(self, violation_results: List[Dict[str, Any]]):
        """
        接收从外部查询得到的结果，并更新微信助手的车辆列表和违章详情。
        """
        self.update_status("正在从查询结果加载数据...")
        
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.plate_violations_map.clear()

        if not violation_results:
            self.update_status("查询结果为空，未加载任何数据。")
            return

        processed_data: Dict[str, Dict[str, Any]] = {}
        for record in violation_results:
            # 车牌号字段映射：优先使用hphm，如果没有则使用_plate
            plate_number = record.get('hphm') or record.get('_plate')
            if not plate_number:
                continue
            
            if plate_number not in processed_data:
                processed_data[plate_number] = {
                    'violations': [], 
                    'total_count': 0, 
                    'pending_count': 0,
                    'latest_violation_time': "",
                    'total_fine': 0,
                    'total_points': 0
                }
            
            processed_data[plate_number]['violations'].append(record)
            processed_data[plate_number]['total_count'] += 1
            
            clbj = record.get('clbj', '0') 
            jkbj = record.get('jkbj', '0')
            
            try:
                fkje_str = record.get('fkje', '0')
                fkje_val = int(float(fkje_str if fkje_str else '0'))
            except ValueError:
                fkje_val = 0
            processed_data[plate_number]['total_fine'] += fkje_val

            try:
                points_str = record.get('_points', '0')
                points_val = int(float(points_str if points_str else '0'))
            except ValueError:
                points_val = 0
            processed_data[plate_number]['total_points'] += points_val

            # 简化的未处理判断逻辑（移除'9'状态）
            try:
                points_str = record.get('_points', '0')
                points_val = int(float(points_str if points_str else '0'))
            except (ValueError, TypeError):
                points_val = 0

            is_pending = False
            # 有记分且未处理
            if points_val > 0 and clbj == '0':
                is_pending = True
            # 有罚款且未缴款
            elif fkje_val > 0 and jkbj == '0':
                is_pending = True

            if is_pending:
                processed_data[plate_number]['pending_count'] += 1
            
            current_v_time = record.get('wfsj', '') 
            if current_v_time:
                if not processed_data[plate_number]['latest_violation_time'] or \
                   current_v_time > processed_data[plate_number]['latest_violation_time']:
                    processed_data[plate_number]['latest_violation_time'] = current_v_time
        
        for plate, data in processed_data.items():
            driver_name = "未分配"
            unique_id = "未知"
            vehicle_info = self.data_manager.get_vehicle_info(plate)
            if vehicle_info:
                driver_name = vehicle_info.get('driver_name', "未分配")
                unique_id = vehicle_info.get('unique_id', "未知")

            send_status = "未发送"
            
            self.plate_violations_map[plate] = {
                'violations': data['violations'], 
                'summary': { 
                    'total': data['total_count'],
                    'pending': data['pending_count'],
                    'plate_number': plate,
                    'total_fine': data['total_fine'],
                    'total_points': data['total_points'],
                    'latest_violation_time': data['latest_violation_time'],
                    'driver_name': driver_name, # 存储司机姓名
                    'unique_id': unique_id    # 存储唯一标识
                },
                'send_status': send_status 
            }
            
            # Corrected item_values order based on defined columns
            item_values = (
                "☐",                       # select
                plate,                     # plate
                data['total_count'],       # violation_count
                data['total_fine'],        # fine_amount
                data['total_points'],      # points
                driver_name,               # driver
                unique_id,                 # unique_id
                send_status                # status
            )
            self.tree.insert("", tk.END, values=item_values, tags=(plate,))
        
        self.update_status(f"成功从查询结果加载了 {len(processed_data)} 个车牌的数据。")

    def get_selected_plates_with_violations(self) -> List[str]:
        """获取选中的车牌号"""
        selected_plates = []
        for item_id in self.tree.get_children():
            try:
                values = self.tree.item(item_id, 'values')
                # Ensure values[0] is boolean True for selected, or string 'true'
                if values and len(values) > 0:
                    selection_val = values[0]
                    is_selected = False
                    if isinstance(selection_val, str) and selection_val == "☑":
                        is_selected = True
                    
                    if is_selected:
                        plate = values[1]  # 车牌号在第二列 (index 1)
                        selected_plates.append(plate)
            except IndexError:
                print(f"Warning: Item {item_id} has unexpected value structure in get_selected_plates_with_violations.")
                continue 
        return selected_plates
    
    def delete_selected_template(self):
        """删除当前在Combobox中选中的模板"""
        selected_name = self.current_selected_template_name.get()
        if not selected_name:
            messagebox.showwarning("提示", "没有选中的模板可删除。", parent=self)
            return

        if not messagebox.askyesno("确认删除", f"确定要删除模板 '{selected_name}' 吗？此操作无法撤销。", parent=self):
            return

        violation_templates = self.templates.get('violation', [])
        template_to_remove = None
        for t in violation_templates:
            if t['name'] == selected_name:
                template_to_remove = t
                break
        
        if template_to_remove:
            violation_templates.remove(template_to_remove)
            
            # 更新Combobox
            new_template_names = [t['name'] for t in violation_templates]
            self.template_combo['values'] = new_template_names
            
            # 清空或选择第一个
            if new_template_names:
                self.current_selected_template_name.set(new_template_names[0])
                self.on_template_select() # 更新文本框内容
            else:
                self.current_selected_template_name.set("")
                self.tips_text.delete('1.0', tk.END) # 确保清空
            
            # 保存更改到文件
            try:
                config_path = self._get_config_path()
                with open(config_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(self.templates, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"模板 '{selected_name}' 已删除。", parent=self)
            except Exception as e:
                messagebox.showerror("错误", f"保存模板文件失败：{str(e)}", parent=self)
        else:
            messagebox.showerror("错误", f"未找到要删除的模板 '{selected_name}'。", parent=self) # 理论上不应发生

    def save_template(self):
        """保存当前提示语为模板。会提示输入模板名称。"""
        current_content = self.tips_text.get('1.0', 'end-1c').strip()
        if not current_content:
            messagebox.showwarning("提示", "提示语内容不能为空", parent=self)
            return

        # 弹出对话框让用户输入模板名称
        from tkinter import simpledialog
        template_name = simpledialog.askstring("保存模板", "请输入模板名称:", parent=self)

        if not template_name:
            return # 用户取消或未输入名称
        
        template_name = template_name.strip()
        if not template_name:
            messagebox.showwarning("提示", "模板名称不能为空", parent=self)
            return

        violation_templates = self.templates.setdefault('violation', [])
        
        # 检查名称是否已存在
        existing_template = None
        for t in violation_templates:
            if t['name'] == template_name:
                existing_template = t
                break
        
        if existing_template: # 名称已存在
            if messagebox.askyesno("确认", f"模板名称 '{template_name}' 已存在，要覆盖吗？", parent=self):
                existing_template['content'] = current_content
            else:
                return # 用户选择不覆盖
        else: # 新模板
            violation_templates.append({'name': template_name, 'content': current_content})
        
        # 更新Combobox
        self.template_combo['values'] = [t['name'] for t in violation_templates]
        self.current_selected_template_name.set(template_name) # 选中刚保存的
            
        # 保存到文件
        try:
            config_path = self._get_config_path()
            with open(config_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(self.templates, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "模板保存成功", parent=self)
        except Exception as e:
            messagebox.showerror("错误", f"保存模板文件失败：{str(e)}", parent=self)
    
    def update_preview_and_clear_selection_on_edit(self):
        """当提示语被编辑时，更新预览并清空当前模板选择（视觉上）"""
        # self.current_selected_template_name.set("") # 暂时不直接清空，避免用户困惑
        self.update_preview()

    def on_template_select(self, event=None):
        """当从Combobox选择一个模板时调用"""
        selected_name = self.current_selected_template_name.get()
        if not selected_name:
            return
        
        for template_data in self.templates.get('violation', []):
            if template_data['name'] == selected_name:
                self.tips_text.delete('1.0', tk.END)
                self.tips_text.insert('1.0', template_data['content'])
                self.update_preview()
                break

    def update_preview(self):
        """更新预览内容"""
        try:
            selected_plates = self.get_selected_plates_with_violations()
            if not selected_plates:
                self.preview_text.delete('1.0', tk.END)
                self.preview_text.insert('1.0', "请选择要发送的车辆")
                return
            
            tips = self.tips_text.get('1.0', 'end-1c').strip()
            if not tips:
                self.preview_text.delete('1.0', tk.END)
                self.preview_text.insert('1.0', "请输入提示语")
                return
            
            preview_content = ""
            actual_driver_name = "车主" # 默认值
            first_plate = selected_plates[0]

            if first_plate in self.plate_violations_map:
                plate_data = self.plate_violations_map[first_plate]
                summary_data = plate_data.get('summary', {})
                actual_driver_name = summary_data.get('driver_name', "车主")
                if not actual_driver_name or actual_driver_name == "未分配": # 如果是未分配，也用默认值
                    actual_driver_name = "车主"
                preview_content = self._construct_violation_message_content(first_plate, plate_data['violations'], summary_data)
            else:
                preview_content = f"车牌 {first_plate} 的详细数据未加载。请先执行查询或检查筛选。"
            
            if not preview_content:
                preview_content = "没有需要提醒的信息或所选车辆无符合条件违章。"
            
            final_preview_message = tips.replace("{driver}", actual_driver_name)
            final_preview_message = final_preview_message.replace("{plate}", first_plate)
            final_preview_message += "\n\n" + preview_content

            self.preview_text.delete('1.0', tk.END)
            self.preview_text.insert('1.0', final_preview_message.strip())
            
        except Exception as e:
            self.preview_text.delete('1.0', tk.END)
            self.preview_text.insert('1.0', f"预览生成失败：{str(e)}\n{traceback.format_exc()}")
    
    def send_messages(self):
        """发送消息"""
        import threading
        
        selected_plates = self.get_selected_plates_with_violations()
        if not selected_plates:
            messagebox.showwarning("提示", "请选择要发送的车辆", parent=self)
            return
        
        tips_template = self.tips_text.get('1.0', 'end-1c').strip()
        if not tips_template:
            messagebox.showwarning("提示", "请输入提示语", parent=self)
            return
        
        self.status_text.delete('1.0', tk.END)
        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.status_text.insert('end', f"开始发送时间：{start_time}\n")
        self.status_text.insert('end', "----------------------------------------\n")
        self.status_text.see('end')
        
        # 获取用户选择的搜索方式
        search_method = self.search_method_var.get()  # 0:车牌号, 1:司机, 2:唯一标识
        
        # 创建发送任务的数据
        send_tasks = []
        for plate in selected_plates:
            if plate not in self.plate_violations_map:
                self.status_text.insert(tk.END, f"ℹ️ 车牌 {plate} 的数据未在当前视图中加载，跳过发送。\n")
                self.plate_violations_map.setdefault(plate, {})['send_status'] = "数据未加载"
                self._update_tree_item_status(plate, "数据未加载")
                continue
                
            plate_data = self.plate_violations_map[plate]
            violations_for_plate = plate_data['violations']
            summary_for_plate = plate_data['summary']
            message_body = self._construct_violation_message_content(plate, violations_for_plate, summary_for_plate)
            
            if not message_body or message_body.strip() == "没有符合条件的违章记录。":
                self.status_text.insert(tk.END, f"ℹ️ 车牌 {plate} 没有符合发送条件的违章信息，跳过。\n")
                self.plate_violations_map[plate]['send_status'] = "无内容跳过"
                self._update_tree_item_status(plate, "无内容跳过")
                continue
                
            final_message = tips_template.replace("{driver}", "车主")
            final_message = final_message.replace("{plate}", plate)
            final_message += "\n\n" + message_body
            
            # 根据搜索方式确定搜索关键字
            search_keyword = plate  # 默认使用车牌号
            search_nickname = plate  # 显示在状态中的名称
            
            # 尝试获取车辆信息以获取司机和唯一标识
            try:
                vehicle_info = None
                for item_id in self.tree.get_children():
                    values = self.tree.item(item_id, 'values')
                    if values and len(values) > 6 and values[1] == plate:
                        # 从树形视图中获取司机和唯一标识
                        driver_name = values[5]  # 司机在第6列
                        unique_id = values[6]   # 唯一标识在第7列
                        vehicle_info = {"driver_name": driver_name, "unique_id": unique_id}
                        break
                
                if not vehicle_info and hasattr(self, 'data_manager'):
                    # 如果没有从树形视图获取到，尝试从数据管理器获取
                    vehicle_info = self.data_manager.get_vehicle_info(plate)
                
                if vehicle_info:
                    if search_method == 1 and vehicle_info.get('driver_name') and vehicle_info.get('driver_name') != "未分配":
                        # 使用司机名称搜索
                        search_keyword = vehicle_info['driver_name']
                        search_nickname = f"{search_keyword}({plate})"
                    elif search_method == 2 and vehicle_info.get('unique_id') and vehicle_info.get('unique_id') != "未知":
                        # 使用唯一标识搜索
                        search_keyword = vehicle_info['unique_id']
                        search_nickname = f"{search_keyword}({plate})"
            except Exception as e:
                self.status_text.insert(tk.END, f"⚠️ 获取 {plate} 的附加信息时出错：{str(e)}，将使用车牌号搜索。\n")
            
            # 将任务添加到队列
            send_tasks.append({
                'plate': plate,
                'search_keyword': search_keyword,
                'search_nickname': search_nickname,
                'message': final_message
            })
        
        if not send_tasks:
            self.status_text.insert(tk.END, "没有需要发送的消息。\n")
            return
            
        # 创建统计变量
        stats = {
            'total_threaded_tasks': len(send_tasks), # 重命名以更清晰
            'success': 0,
            'failed': 0,
            'processed_threaded_tasks': 0 # 重命名以更清晰
        }
        
        # 记录初始跳过的任务数量 (在创建send_tasks之前的计数)
        initial_skipped_count = len(selected_plates) - len(send_tasks)
        
        # 更新UI的函数
        def update_status(plate, status, message=None, is_final_status_for_task=False):
            def _update():
                if message:
                    self.status_text.insert(tk.END, message + "\n")
                    self.status_text.see(tk.END)
                
                # 更新内存中的状态
                self.plate_violations_map.setdefault(plate, {})['send_status'] = status
                
                # 更新Treeview中的状态显示
                self._update_tree_item_status(plate, status)
                
                if is_final_status_for_task:
                    if status == "发送成功":
                        stats['success'] += 1
                    elif status in ["发送失败", "发送异常"]:
                        stats['failed'] += 1
                    # 其他最终状态 (如果未来有) 不会直接计入 success/failed，会体现在差额中
                    
                    stats['processed_threaded_tasks'] += 1 # 增加已处理的线程任务计数
                
                    # 如果所有线程任务完成，显示总结
                    if stats['processed_threaded_tasks'] >= stats['total_threaded_tasks']:
                        end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        self.status_text.insert('end', "\n----------------------------------------\n")
                        self.status_text.insert('end', f"发送完成时间：{end_time}\n")
                        self.status_text.insert('end', f"发送统计：\n")
                        self.status_text.insert('end', f"总计选择车辆：{len(selected_plates)} 条\n")
                        if initial_skipped_count > 0:
                            self.status_text.insert('end', f"初始跳过(无内容/数据)：{initial_skipped_count} 条\n")
                        self.status_text.insert('end', f"尝试发送(进入线程)：{stats['total_threaded_tasks']} 条\n")
                        self.status_text.insert('end', f"  发送成功：{stats['success']} 条\n")
                        self.status_text.insert('end', f"  发送失败/异常：{stats['failed']} 条\n")
                        
                        # 计算未明确成功或失败的线程内任务 (理论上应为0)
                        threaded_other_outcomes = stats['total_threaded_tasks'] - stats['success'] - stats['failed']
                        if threaded_other_outcomes > 0:
                            self.status_text.insert('end', f"  线程中其他结果：{threaded_other_outcomes} 条\n")
                        self.status_text.insert('end', "----------------------------------------\n")
                        self.status_text.see('end')
            
            # 在主线程中执行UI更新
            if threading.current_thread() is threading.main_thread():
                _update()
            else:
                self.after(0, _update)
        
        # 发送消息的函数
        def send_task_thread():
            for task in send_tasks:
                plate = task['plate']
                search_keyword = task['search_keyword']
                search_nickname = task['search_nickname']
                message = task['message']
                
                try:
                    # 更新状态为发送中 (非最终状态)
                    update_status(plate, "发送中", f"🔍 正在搜索并发送给：{search_nickname}", is_final_status_for_task=False)
                    
                    # 回调函数，用于接收发送过程中的状态更新 (非最终状态)
                    def send_callback(msg):
                        update_status(plate, "发送中", msg, is_final_status_for_task=False)
                    
                    # 执行发送
                    send_successful = self.wechat.send_message(
                        {'nickname': search_nickname, 'remark': search_keyword}, 
                        message,
                        send_callback
                    )
                    
                    # 更新最终状态
                    if send_successful:
                        update_status(plate, "发送成功", f"✅ 发送给 {search_nickname} 成功", is_final_status_for_task=True)
                    else:
                        update_status(plate, "发送失败", f"❌ 发送给 {search_nickname} 失败", is_final_status_for_task=True)
                        
                except Exception as e:
                    update_status(plate, "发送异常", f"❌ 发送给 {search_nickname} 失败（异常）：{str(e)}", is_final_status_for_task=True)
        
        # 启动发送线程
        send_thread = threading.Thread(target=send_task_thread)
        send_thread.daemon = True  # 设置为守护线程，这样当主线程退出时，发送线程也会退出
        send_thread.start()

    def _update_tree_item_status(self, plate_number: str, status: str):
        """更新Treeview中指定车牌行的状态列"""
        for item_id in self.tree.get_children():
            values = list(self.tree.item(item_id, 'values'))
            if values and len(values) > 1 and values[1] == plate_number: # 车牌号在第2列 (index 1)
                if len(values) > 7: # 确保状态列存在 (index 7，因为增加了两列)
                    values[7] = status
                    self.tree.item(item_id, values=tuple(values))
                    break 
                else: # 如果列数不够，可能是个旧的行数据结构，尝试添加
                    # 这通常不应发生，除非动态改变列结构而不刷新
                    print(f"DEBUG: 列数不足，尝试为 {plate_number} 更新状态列 ({len(values)}列)")
                    # pass # 或者更安全的做法是重新加载该行，但简单更新可能导致数据错位
                    # 如果确实需要动态添加，需要更复杂的处理，或者保证列数总是正确的
                    # 暂时我们假设列数是正确的
                    break

    def update_status(self, message: str):
        """更新状态显示"""
        self.status_text.insert('end', message + "\n")
        self.status_text.see('end')
    
    def on_tree_click(self, event):
        """处理表格单击事件 - 切换选择状态或处理表头点击"""
        region = self.tree.identify_region(event.x, event.y)
        item_id = self.tree.identify_row(event.y)

        if region == "heading":
            column_id = self.tree.identify_column(event.x)
            col_index = int(column_id.replace('#', '')) - 1
            if col_index == 0:  # Clicked on "Select" header
                self.toggle_select_all()
            return

        if not item_id: # Clicked on empty space below rows
            return
            
        # Clicked on a row, toggle the checkbox
        current_values = list(self.tree.item(item_id, 'values'))
        if not current_values:
            return

        current_symbol = current_values[0]
        if isinstance(current_symbol, str) and current_symbol == "☑":
            new_symbol = "☐"
        else:
            new_symbol = "☑"
        
        current_values[0] = new_symbol
        self.tree.item(item_id, values=tuple(current_values))
        self.update_preview() # 更新预览

    def on_tree_double_click(self, event):
        """处理表格双击事件 - 显示详情"""
        item_id = self.tree.identify_row(event.y)
        if not item_id:
            return

        current_values = list(self.tree.item(item_id, 'values'))
        if not current_values or len(current_values) < 2:
            return
        
        plate_number = current_values[1] # 车牌号在第二列

        if hasattr(self, 'db') and self.db:
            ViolationDetailDialog(self.parent, plate_number, self.db)
        else:
            messagebox.showerror("错误", "数据库连接不可用，无法显示详情。", parent=self.parent)
            print("Error: self.db not available for ViolationDetailDialog")

    def on_tree_select(self, event):
        """处理表格选择事件"""
        self.update_preview()

    def toggle_select_all(self):
        """切换所有车辆的选择状态 (全选/全不选)"""
        items = self.tree.get_children()
        if not items:
            return

        all_currently_selected = True
        for item_id in items:
            try:
                item_values = self.tree.item(item_id, 'values')
                # Check if the first element (selection state) is False or not a boolean
                if not item_values or len(item_values) == 0:
                    all_currently_selected = False
                    break
                
                selection_val = item_values[0]
                is_item_selected = False
                if isinstance(selection_val, str) and selection_val == "☑":
                    is_item_selected = True
                
                if not is_item_selected:
                    all_currently_selected = False
                    break
            except (IndexError, TypeError): # Catch issues if item_values is not as expected
                all_currently_selected = False
                print(f"Warning: Problematic item {item_id} in toggle_select_all.")
                break
        
        new_selection_state = not all_currently_selected
        new_symbol = "☑" if new_selection_state else "☐"

        for item_id in items:
            try:
                current_values = list(self.tree.item(item_id, 'values'))
                if current_values: # Ensure there are values to modify
                    current_values[0] = new_symbol
                    self.tree.item(item_id, values=tuple(current_values))
            except Exception as e:
                print(f"Error updating item {item_id} in toggle_select_all: {e}")
        
        self.update_preview()

    def _on_paned_configure(self, event):
        """当PanedWindow配置(如大小改变)时调用，用于设置初始分割比例和表格列宽"""
        # 设置PanedWindow的分割比例
        if not self._initial_sash_set and self.paned.winfo_width() > 1:
            paned_total_width = self.paned.winfo_width()
            desired_right_pane_width = 400 
            min_left_pane_width = 350 
            calculated_left_pane_width = paned_total_width - desired_right_pane_width
            sash_position = calculated_left_pane_width
            if calculated_left_pane_width < min_left_pane_width:
                sash_position = min_left_pane_width
            if sash_position < 0:
                sash_position = 0
            if sash_position >= paned_total_width - 50: 
                sash_position = paned_total_width - 50
            try:
                self.paned.sashpos(0, sash_position)
                self._initial_sash_set = True 
            except tk.TclError: 
                pass
        
        # 动态调整表格列宽
        # 确保left_frame已经绘制并有宽度
        self.left_frame.update_idletasks() # 确保winfo_width获取的是更新后的宽度
        left_frame_width = self.left_frame.winfo_width()
        
        if left_frame_width > 0 and hasattr(self, 'tree') and hasattr(self, 'column_configs'):
            total_minwidth = sum(cc[3] for cc in self.column_configs)
            total_current_width = sum(self.tree.column(cc[0], "width") for cc in self.column_configs)
            available_width_for_tree = left_frame_width - 20 # 减去一些padding和滚动条估算值

            if available_width_for_tree <= total_minwidth:
                # 如果可用空间不足以容纳所有列的最小宽度，则按最小宽度设置
                for col_id, _, _, min_w in self.column_configs:
                    self.tree.column(col_id, width=min_w)
            else:
                # 如果空间充足，按比例分配剩余空间
                # 首先，计算总的期望宽度（基于初始配置的width）
                total_desired_width = sum(cc[2] for cc in self.column_configs)
                scale_factor = available_width_for_tree / total_desired_width if total_desired_width > 0 else 1

                for col_id, _, desired_w, min_w in self.column_configs:
                    new_width = int(desired_w * scale_factor)
                    # 确保新宽度不小于最小宽度
                    final_width = max(new_width, min_w)
                    self.tree.column(col_id, width=final_width)

    def _is_valid_date_format(self, date_str):
        """检查日期字符串是否为 YYYY-MM-DD 格式，允许空字符串"""
        if not date_str: # 允许空字符串 (表示未选择日期)
            return True 
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
            return True
        except ValueError:
            return False

    def _filter_data_if_valid_dates(self, event=None): # event 参数是 <<DateEntrySelected>> 传递的
        """如果两个日期都有效（或为空），则尝试加载数据"""
        start_date_str = self.start_date_entry.get() # 直接获取字符串
        end_date_str = self.end_date_entry.get()   # 直接获取字符串

        valid_start = self._is_valid_date_format(start_date_str)
        valid_end = self._is_valid_date_format(end_date_str)
        
        if not valid_start:
            print(f"警告: 开始日期格式无效: {start_date_str}")
            # 这里可以考虑给用户更明显的提示，例如弹窗或改变输入框样式
            return 

        if not valid_end:
            print(f"警告: 结束日期格式无效: {end_date_str}")
            return

        if valid_start and valid_end: 
            self.load_data()

    def _construct_violation_message_content(self, plate_number: str, violations: List[Dict], summary: Dict) -> str:
        """根据选中的字段构造单个车牌的违章消息主体内容。"""
        message_parts = []
        pending_violations_to_send = []

        if violations:
            for v_detail in violations:
                clbj = v_detail.get('process_flag', '0')
                jkbj = v_detail.get('payment_flag', '0')
                fkje = 0
                try:
                    fkje = int(float(v_detail.get('fine_amount', 0)))
                except (ValueError, TypeError):
                    pass # fkje remains 0
                
                # 简化的未处理判断逻辑（移除'9'状态）
                try:
                    points_val = int(float(v_detail.get('points', 0)))
                except (ValueError, TypeError):
                    points_val = 0

                is_pending_detail = False
                # 有记分且未处理
                if points_val > 0 and clbj == '0':
                    is_pending_detail = True
                # 有罚款且未缴款
                elif fkje > 0 and jkbj == '0':
                    is_pending_detail = True
                
                if is_pending_detail:
                    pending_violations_to_send.append(v_detail)
        
        # --- 处理未处理违章 --- 
        if pending_violations_to_send:
            if self.field_vars.get("summary_stats", tk.BooleanVar(value=False)).get():
                actual_pending_count = len(pending_violations_to_send)
                actual_total_fine = sum(float(v.get('fine_amount', 0) or 0) for v in pending_violations_to_send)
                actual_total_points = sum(int(v.get('points', 0) or 0) for v in pending_violations_to_send)

                message_parts.append("【违章统计 (未处理部分)】")
                message_parts.append(f"未处理违章：{actual_pending_count}次")
                message_parts.append(f"总罚款金额：{actual_total_fine}元")
                message_parts.append(f"总记分：{actual_total_points}分")
                message_parts.append("--------------------------------")

            details_parts = []
            for i, v in enumerate(pending_violations_to_send, 1):
                violation_detail_str_parts = []
                # ... (原有字段选择逻辑不变) ...
                if self.field_vars.get("violation_time", tk.BooleanVar(value=False)).get():
                    violation_detail_str_parts.append(f"违章时间：{v.get('violation_time', 'N/A')}")
                if self.field_vars.get("violation_location", tk.BooleanVar(value=False)).get():
                    violation_detail_str_parts.append(f"违章地点：{v.get('violation_location', 'N/A')}")
                if self.field_vars.get("violation_content", tk.BooleanVar(value=False)).get():
                    violation_detail_str_parts.append(f"违章内容：{v.get('violation_content', 'N/A')}")
                if self.field_vars.get("fine_amount", tk.BooleanVar(value=False)).get():
                    violation_detail_str_parts.append(f"罚款金额：{v.get('fine_amount', '0')}元")
                if self.field_vars.get("points", tk.BooleanVar(value=False)).get():
                    violation_detail_str_parts.append(f"记分：{v.get('points', '0')}分")
                
                process_text, payment_text = self._get_status_texts(v)
                if self.field_vars.get("process_flag", tk.BooleanVar(value=False)).get():
                     violation_detail_str_parts.append(f"记分状态：{process_text}")
                if self.field_vars.get("payment_flag", tk.BooleanVar(value=False)).get():
                     violation_detail_str_parts.append(f"罚款状态：{payment_text}")

                if violation_detail_str_parts:
                    details_parts.append(f"▶ 未处理违章 {i}") # 区分标题
                    details_parts.extend(violation_detail_str_parts)
                    if i < len(pending_violations_to_send):
                        details_parts.append("--------------------------------")
            
            if details_parts:
                message_parts.append("【未处理违章明细】") # 区分标题
                message_parts.extend(details_parts)
        elif self.field_vars.get("summary_stats", tk.BooleanVar(value=False)).get() and summary.get('total', 0) > 0:
            # 即使没有未处理违章，如果选了统计，也显示整体统计
            message_parts.append(f"车牌号码：{plate_number}")
            message_parts.append("【总体统计信息】")
            message_parts.append(f"总违章次数 (符合日期筛选): {summary.get('total', 0)}次")
            message_parts.append(f"其中未处理 (之前计算的): {summary.get('pending', 0)}次")
            message_parts.append("当前筛选条件下，无未处理违章详情可发送。")
        
        # --- 新增：处理已处理违章 (如果勾选) ---
        if self.field_vars.get("include_processed", tk.BooleanVar(value=False)).get() and violations:
            processed_violations_to_send = []
            for v_detail in violations:
                clbj = v_detail.get('process_flag', '0')
                jkbj = v_detail.get('payment_flag', '0')
                fkje = 0
                try:
                    fkje = int(float(v_detail.get('fine_amount', 0)))
                except (ValueError, TypeError):
                    pass # fkje remains 0
                
                # 简化的未处理判断逻辑（移除'9'状态）
                try:
                    points_val = int(float(v_detail.get('points', 0)))
                except (ValueError, TypeError):
                    points_val = 0

                is_pending_detail = False
                # 有记分且未处理
                if points_val > 0 and clbj == '0':
                    is_pending_detail = True
                # 有罚款且未缴款
                elif fkje > 0 and jkbj == '0':
                    is_pending_detail = True
                
                if not is_pending_detail: # 这条是已处理的
                    processed_violations_to_send.append(v_detail)
            
            if processed_violations_to_send:
                if message_parts: # 如果前面有内容，加个分隔
                    message_parts.append("\n================================")
                message_parts.append("【已处理违章记录】")
                
                processed_details_parts = []
                for i, v in enumerate(processed_violations_to_send, 1):
                    violation_detail_str_parts = []
                    # (与上面未处理违章相同的字段选择逻辑)
                    if self.field_vars.get("violation_time", tk.BooleanVar(value=False)).get():
                        violation_detail_str_parts.append(f"违章时间：{v.get('violation_time', 'N/A')}")
                    if self.field_vars.get("violation_location", tk.BooleanVar(value=False)).get():
                        violation_detail_str_parts.append(f"违章地点：{v.get('violation_location', 'N/A')}")
                    if self.field_vars.get("violation_content", tk.BooleanVar(value=False)).get():
                        violation_detail_str_parts.append(f"违章内容：{v.get('violation_content', 'N/A')}")
                    if self.field_vars.get("fine_amount", tk.BooleanVar(value=False)).get():
                        violation_detail_str_parts.append(f"罚款金额：{v.get('fine_amount', '0')}元")
                    if self.field_vars.get("points", tk.BooleanVar(value=False)).get():
                        violation_detail_str_parts.append(f"记分：{v.get('points', '0')}分")
                    
                    process_text, payment_text = self._get_status_texts(v)
                    if self.field_vars.get("process_flag", tk.BooleanVar(value=False)).get():
                         violation_detail_str_parts.append(f"记分状态：{process_text}")
                    if self.field_vars.get("payment_flag", tk.BooleanVar(value=False)).get():
                         violation_detail_str_parts.append(f"罚款状态：{payment_text}")

                    if violation_detail_str_parts:
                        processed_details_parts.append(f"▶ 已处理违章 {i}") # 区分标题
                        processed_details_parts.extend(violation_detail_str_parts)
                        if i < len(processed_violations_to_send):
                            processed_details_parts.append("--------------------------------")
                message_parts.extend(processed_details_parts)

        if not message_parts: # 如果最终什么内容都没有
             return "没有符合条件的违章记录可供发送。"
             
        return "\n".join(message_parts)

    def _get_status_texts(self, violation_detail: Dict) -> tuple[str, str]:
        """辅助函数，根据违章记录获取处理和缴款状态的文本描述"""
        process_text = "未知"
        payment_text = "未知"
        clbj = violation_detail.get('process_flag','')
        jkbj = violation_detail.get('payment_flag','')
        fkje_val = 0
        try: 
            fkje_val = float(violation_detail.get('fine_amount',0) or 0) 
        except (ValueError, TypeError):
            pass

        # 简化的状态文本（移除'9'状态）
        if clbj == '1':
            process_text = "已处理"
        else:
            process_text = "未处理"

        if fkje_val == 0:
            payment_text = "无需缴款"
        elif jkbj == '1':
            payment_text = "已缴款"
        else:
            payment_text = "未缴款"
        
        return process_text, payment_text

def main():
    root = tk.Tk()
    app = WechatAssistantTk(root)
    root.mainloop()

if __name__ == "__main__":
    main()