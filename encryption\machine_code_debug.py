import wmi
import hashlib
import time
from encryption.config import AUTH_CONFIG

class MachineCodeDebugger:
    def __init__(self):
        self.pre_str = AUTH_CONFIG["pre_str"]
        self.suf_str = AUTH_CONFIG["suf_str"]
        self.m_wmi = wmi.WMI()

    def get_cpu_serial(self):
        """获取CPU序列号(16位)"""
        try:
            cpu_info = self.m_wmi.Win32_Processor()
            if len(cpu_info) > 0:
                serial = cpu_info[0].ProcessorId
                print(f"CPU序列号: {serial}")
                return serial
            else:
                print("CPU序列号: 未找到CPU信息，使用默认值")
                return "ABCDEFGHIJKLMNOP"
        except Exception as e:
            print(f"获取CPU序列号错误: {e}")
            return "ABCDEFGHIJKLMNOP"

    def get_disk_serial(self):
        """获取硬盘序列号(15位)"""
        try:
            disk_info = self.m_wmi.Win32_PhysicalMedia()
            print(f"找到 {len(disk_info)} 个物理磁盘")
            
            for i, disk in enumerate(disk_info):
                print(f"磁盘 {i}: SerialNumber = {disk.SerialNumber}")
            
            if disk_info and len(disk_info) > 0 and disk_info[0].SerialNumber:
                serial = disk_info[0].SerialNumber.strip()
                print(f"使用磁盘序列号: {serial}")
                return serial
            else:
                print("磁盘序列号: 未找到有效序列号，使用默认值")
                return "DEFAULT_SERIAL"
        except Exception as e:
            print(f"获取磁盘序列号错误: {str(e)}")
            return "DEFAULT_SERIAL"

    def get_mac_address(self):
        """获取MAC地址(12位)"""
        try:
            network_configs = self.m_wmi.Win32_NetworkAdapterConfiguration()
            print(f"找到 {len(network_configs)} 个网络适配器配置")
            
            valid_macs = []
            for i, network in enumerate(network_configs):
                if network.MacAddress:
                    mac = network.MacAddress.replace(":", "")
                    print(f"网络适配器 {i}: MAC = {network.MacAddress} -> {mac}")
                    print(f"  - 描述: {network.Description}")
                    print(f"  - IP启用: {network.IPEnabled}")
                    valid_macs.append((mac, network.IPEnabled, network.Description))
            
            # 优先选择启用IP的网络适配器
            for mac, ip_enabled, desc in valid_macs:
                if ip_enabled:
                    print(f"选择启用IP的MAC地址: {mac} ({desc})")
                    return mac
            
            # 如果没有启用IP的，选择第一个有效的MAC
            if valid_macs:
                mac = valid_macs[0][0]
                print(f"选择第一个有效MAC地址: {mac}")
                return mac
            
            print("MAC地址: 未找到有效MAC地址，使用默认值")
            return "ABCDEF123456"
        except Exception as e:
            print(f"获取MAC地址错误: {e}")
            return "ABCDEF123456"

    def get_board_serial(self):
        """获取主板序列号(14位)"""
        try:
            board_info = self.m_wmi.Win32_BaseBoard()
            print(f"找到 {len(board_info)} 个主板信息")
            
            for i, board in enumerate(board_info):
                print(f"主板 {i}: SerialNumber = '{board.SerialNumber}'")
                print(f"  - 制造商: {board.Manufacturer}")
                print(f"  - 产品: {board.Product}")
            
            if len(board_info) > 0:
                serial = board_info[0].SerialNumber.strip().strip('.')
                print(f"使用主板序列号: '{serial}'")
                return serial
            else:
                print("主板序列号: 未找到主板信息，使用默认值")
                return "ABCDEFGHIJKLMN"
        except Exception as e:
            print(f"获取主板序列号错误: {e}")
            return "ABCDEFGHIJKLMN"

    def get_machine_code_debug(self):
        """生成机器码并显示详细信息"""
        print("=" * 60)
        print("机器码生成调试信息")
        print("=" * 60)
        
        print(f"前缀: {self.pre_str}")
        mac = self.get_mac_address()
        cpu = self.get_cpu_serial()
        disk = self.get_disk_serial()
        board = self.get_board_serial()
        print(f"后缀: {self.suf_str}")
        
        print("\n组合字符串组成:")
        print(f"前缀: '{self.pre_str}'")
        print(f"MAC: '{mac}'")
        print(f"CPU: '{cpu}'")
        print(f"磁盘: '{disk}'")
        print(f"主板: '{board}'")
        print(f"后缀: '{self.suf_str}'")
        
        combine_str = self.pre_str + mac + cpu + disk + board + self.suf_str
        print(f"\n完整组合字符串: '{combine_str}'")
        print(f"组合字符串长度: {len(combine_str)}")
        
        machine_code = hashlib.md5(combine_str.encode("utf-8")).hexdigest().upper()
        print(f"\n最终机器码: {machine_code}")
        print("=" * 60)
        
        return machine_code

    def test_stability(self, iterations=5, delay=2):
        """测试机器码稳定性"""
        print(f"\n开始机器码稳定性测试 (测试 {iterations} 次，间隔 {delay} 秒)")
        print("=" * 80)
        
        codes = []
        for i in range(iterations):
            print(f"\n第 {i+1} 次测试:")
            code = self.get_machine_code_debug()
            codes.append(code)
            
            if i < iterations - 1:
                print(f"等待 {delay} 秒...")
                time.sleep(delay)
        
        print("\n" + "=" * 80)
        print("稳定性测试结果:")
        print("=" * 80)
        
        unique_codes = set(codes)
        if len(unique_codes) == 1:
            print("✅ 机器码稳定 - 所有测试生成相同的机器码")
        else:
            print("❌ 机器码不稳定 - 发现不同的机器码:")
            for i, code in enumerate(codes):
                print(f"  第 {i+1} 次: {code}")
        
        return len(unique_codes) == 1

if __name__ == "__main__":
    debugger = MachineCodeDebugger()
    
    # 单次详细测试
    debugger.get_machine_code_debug()
    
    # 稳定性测试
    is_stable = debugger.test_stability(iterations=3, delay=1)
    
    if not is_stable:
        print("\n🔧 建议的解决方案:")
        print("1. 检查网络适配器是否频繁变化")
        print("2. 考虑使用更稳定的硬件特征")
        print("3. 添加机器码缓存机制")
