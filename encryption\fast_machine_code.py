"""
快速机器码生成器

专门用于快速启动的简化版机器码生成器
优先使用缓存和快速方法，减少启动时间
"""

import os
import json
import hashlib
import uuid
import subprocess
from encryption.config import AUTH_CONFIG

class FastMachineCodeGenerator:
    """快速机器码生成器 - 专注于启动速度"""
    
    def __init__(self):
        self.pre_str = AUTH_CONFIG["pre_str"]
        self.suf_str = AUTH_CONFIG["suf_str"]
        self._cache_file = self._get_cache_file_path()
        self._cached_components = None
    
    def _get_cache_file_path(self):
        """获取缓存文件路径"""
        import tempfile
        cache_dir = os.path.join(tempfile.gettempdir(), "YiChengTech")
        os.makedirs(cache_dir, exist_ok=True)
        return os.path.join(cache_dir, "fast_machine_components.cache")
    
    def _load_cached_components(self):
        """加载缓存的硬件组件信息"""
        try:
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return None
    
    def _save_cached_components(self, components):
        """保存硬件组件信息到缓存"""
        try:
            with open(self._cache_file, 'w', encoding='utf-8') as f:
                json.dump(components, f, ensure_ascii=False, indent=2)
        except Exception:
            pass
    
    def get_fast_mac_address(self):
        """快速获取MAC地址"""
        try:
            # 方法1：使用uuid.getnode()（最快）
            mac_int = uuid.getnode()
            mac_hex = f"{mac_int:012x}".upper()
            if mac_hex != "000000000000":
                return mac_hex
        except:
            pass
        
        try:
            # 方法2：使用getmac命令
            output = subprocess.check_output(['getmac', '/fo', 'csv'], 
                                           universal_newlines=True, 
                                           timeout=2)
            lines = output.strip().split('\n')[1:]
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 1:
                        mac = parts[0].strip('"').replace('-', '').replace(':', '')
                        if mac and mac != "000000000000":
                            return mac
        except:
            pass
        
        return "ABCDEF123456"
    
    def get_fast_disk_serial(self):
        """快速获取磁盘序列号"""
        try:
            # 使用wmic命令快速获取
            output = subprocess.check_output([
                'wmic', 'diskdrive', 'get', 'serialnumber', '/format:csv'
            ], universal_newlines=True, timeout=2)
            
            lines = output.strip().split('\n')[1:]
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 2:
                        serial = parts[1].strip()
                        if serial and serial not in ["", "SerialNumber"]:
                            return serial
        except:
            pass
        
        return "DEFAULT_SERIAL"
    
    def get_fast_cpu_serial(self):
        """快速获取CPU序列号"""
        try:
            output = subprocess.check_output([
                'wmic', 'cpu', 'get', 'processorid', '/format:csv'
            ], universal_newlines=True, timeout=2)
            
            lines = output.strip().split('\n')[1:]
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 2:
                        serial = parts[1].strip()
                        if serial and serial != "ProcessorId":
                            return serial
        except:
            pass
        
        return "ABCDEFGHIJKLMNOP"
    
    def get_fast_board_serial(self):
        """快速获取主板序列号"""
        try:
            output = subprocess.check_output([
                'wmic', 'baseboard', 'get', 'serialnumber', '/format:csv'
            ], universal_newlines=True, timeout=2)
            
            lines = output.strip().split('\n')[1:]
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 2:
                        serial = parts[1].strip().strip('.')
                        if serial and serial not in ["SerialNumber", "Default string", "To be filled by O.E.M.", "None", ""]:
                            return serial
        except:
            pass
        
        return "ABCDEFGHIJKLMN"
    
    def get_system_uuid(self):
        """获取系统UUID"""
        try:
            output = subprocess.check_output([
                'wmic', 'csproduct', 'get', 'uuid', '/format:csv'
            ], universal_newlines=True, timeout=2)
            
            lines = output.strip().split('\n')[1:]
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 2:
                        uuid_str = parts[1].strip()
                        if uuid_str and uuid_str != "UUID" and uuid_str != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF":
                            return uuid_str.replace("-", "")[:16]
        except:
            pass
        
        return "DEFAULT_UUID_16"
    
    def get_hardware_components_fast(self, use_cache=True):
        """快速获取硬件组件信息"""
        if use_cache and self._cached_components:
            return self._cached_components
        
        if use_cache:
            cached = self._load_cached_components()
            if cached:
                self._cached_components = cached
                return cached
        
        # 快速获取硬件信息（总超时时间3秒）
        components = {
            'cpu_serial': self.get_fast_cpu_serial(),
            'disk_serial': self.get_fast_disk_serial(),
            'mac_address': self.get_fast_mac_address(),
            'board_serial': self.get_fast_board_serial(),
            'system_uuid': self.get_system_uuid(),
            'timestamp': str(int(os.path.getctime(__file__)))
        }
        
        self._cached_components = components
        if use_cache:
            self._save_cached_components(components)
        
        return components
    
    def get_machine_code(self, use_cache=True):
        """生成机器码（快速版本）"""
        try:
            components = self.get_hardware_components_fast(use_cache)
            
            combine_str = (
                self.pre_str + 
                components['mac_address'] + 
                components['cpu_serial'] + 
                components['disk_serial'] + 
                components['board_serial'] + 
                components['system_uuid'] + 
                self.suf_str
            )
            
            # 使用SHA-256
            machine_code = hashlib.sha256(combine_str.encode("utf-8")).hexdigest().upper()
            return machine_code
            
        except Exception as e:
            print(f"快速生成机器码错误: {e}")
            # 备用方案
            fallback_str = self.pre_str + "FAST_FALLBACK_CODE" + self.suf_str
            return hashlib.sha256(fallback_str.encode("utf-8")).hexdigest().upper()
    
    def clear_cache(self):
        """清除缓存"""
        self._cached_components = None
        try:
            if os.path.exists(self._cache_file):
                os.remove(self._cache_file)
        except Exception:
            pass
