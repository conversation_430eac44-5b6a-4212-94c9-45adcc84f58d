('e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\ViolationQueryApp.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'E:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main',
   'E:\\Software\\009automation\\violation\\违章查询_new\\main.py',
   'PYSOURCE')],
 'python312.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
