('e:\\Software\\009automation\\violation\\违章查询_new\\build\\ViolationQueryApp\\PYZ-00.pyz',
 [('Crypto',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kw',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kwp',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Random',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('PIL',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt6',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('babel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\__init__.py',
   'PYMODULE'),
  ('babel.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\core.py',
   'PYMODULE'),
  ('babel.dates',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\dates.py',
   'PYMODULE'),
  ('babel.localedata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\localedata.py',
   'PYMODULE'),
  ('babel.localtime',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\localtime\\__init__.py',
   'PYMODULE'),
  ('babel.localtime._fallback',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\localtime\\_fallback.py',
   'PYMODULE'),
  ('babel.localtime._helpers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\localtime\\_helpers.py',
   'PYMODULE'),
  ('babel.localtime._unix',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\localtime\\_unix.py',
   'PYMODULE'),
  ('babel.localtime._win32',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\localtime\\_win32.py',
   'PYMODULE'),
  ('babel.numbers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\numbers.py',
   'PYMODULE'),
  ('babel.plural',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\babel\\plural.py',
   'PYMODULE'),
  ('backports',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('comtypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\__init__.py',
   'PYMODULE'),
  ('comtypes.GUID',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\GUID.py',
   'PYMODULE'),
  ('comtypes._comobject',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_comobject.py',
   'PYMODULE'),
  ('comtypes._memberspec',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_memberspec.py',
   'PYMODULE'),
  ('comtypes._meta',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_meta.py',
   'PYMODULE'),
  ('comtypes._npsupport',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_npsupport.py',
   'PYMODULE'),
  ('comtypes._post_coinit',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_post_coinit\\__init__.py',
   'PYMODULE'),
  ('comtypes._post_coinit._cominterface_meta_patcher',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_post_coinit\\_cominterface_meta_patcher.py',
   'PYMODULE'),
  ('comtypes._post_coinit.bstr',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_post_coinit\\bstr.py',
   'PYMODULE'),
  ('comtypes._post_coinit.instancemethod',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_post_coinit\\instancemethod.py',
   'PYMODULE'),
  ('comtypes._post_coinit.misc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_post_coinit\\misc.py',
   'PYMODULE'),
  ('comtypes._post_coinit.unknwn',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_post_coinit\\unknwn.py',
   'PYMODULE'),
  ('comtypes._safearray',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_safearray.py',
   'PYMODULE'),
  ('comtypes._tlib_version_checker',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_tlib_version_checker.py',
   'PYMODULE'),
  ('comtypes._vtbl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\_vtbl.py',
   'PYMODULE'),
  ('comtypes.automation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\automation.py',
   'PYMODULE'),
  ('comtypes.client',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\__init__.py',
   'PYMODULE'),
  ('comtypes.client._activeobj',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_activeobj.py',
   'PYMODULE'),
  ('comtypes.client._code_cache',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_code_cache.py',
   'PYMODULE'),
  ('comtypes.client._constants',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_constants.py',
   'PYMODULE'),
  ('comtypes.client._create',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_create.py',
   'PYMODULE'),
  ('comtypes.client._events',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_events.py',
   'PYMODULE'),
  ('comtypes.client._generate',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_generate.py',
   'PYMODULE'),
  ('comtypes.client._managing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\_managing.py',
   'PYMODULE'),
  ('comtypes.client.dynamic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\dynamic.py',
   'PYMODULE'),
  ('comtypes.client.lazybind',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\client\\lazybind.py',
   'PYMODULE'),
  ('comtypes.connectionpoints',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\connectionpoints.py',
   'PYMODULE'),
  ('comtypes.errorinfo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\errorinfo.py',
   'PYMODULE'),
  ('comtypes.gen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py',
   'PYMODULE'),
  ('comtypes.hresult',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\hresult.py',
   'PYMODULE'),
  ('comtypes.messageloop',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\messageloop.py',
   'PYMODULE'),
  ('comtypes.patcher',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\patcher.py',
   'PYMODULE'),
  ('comtypes.persist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\persist.py',
   'PYMODULE'),
  ('comtypes.safearray',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\safearray.py',
   'PYMODULE'),
  ('comtypes.server',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\server\\__init__.py',
   'PYMODULE'),
  ('comtypes.stream',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\stream.py',
   'PYMODULE'),
  ('comtypes.tools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\__init__.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\__init__.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.codegenerator',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\codegenerator.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.comments',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\comments.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.heads',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\heads.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.helpers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\helpers.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.modulenamer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\modulenamer.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.namespaces',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\namespaces.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.packing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\packing.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.typeannotator',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\typeannotator.py',
   'PYMODULE'),
  ('comtypes.tools.tlbparser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\tlbparser.py',
   'PYMODULE'),
  ('comtypes.tools.typedesc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\typedesc.py',
   'PYMODULE'),
  ('comtypes.tools.typedesc_base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\tools\\typedesc_base.py',
   'PYMODULE'),
  ('comtypes.typeinfo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\comtypes\\typeinfo.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\copy.py',
   'PYMODULE'),
  ('core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\__init__.py',
   'PYMODULE'),
  ('core.chart_generator',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\chart_generator.py',
   'PYMODULE'),
  ('core.data_exporter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\data_exporter.py',
   'PYMODULE'),
  ('core.data_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\data_manager.py',
   'PYMODULE'),
  ('core.email_sender',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\email_sender.py',
   'PYMODULE'),
  ('core.rental_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\rental_manager.py',
   'PYMODULE'),
  ('core.vehicle_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\vehicle_manager.py',
   'PYMODULE'),
  ('core.violation_query',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\violation_query.py',
   'PYMODULE'),
  ('core.wechat_assistant_tk',
   'e:\\Software\\009automation\\violation\\违章查询_new\\core\\wechat_assistant_tk.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('cycler',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\image.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('encryption',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\__init__.py',
   'PYMODULE'),
  ('encryption.aes_crypto',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\aes_crypto.py',
   'PYMODULE'),
  ('encryption.auth_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\auth_manager.py',
   'PYMODULE'),
  ('encryption.cloud_storage',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\cloud_storage.py',
   'PYMODULE'),
  ('encryption.config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\config.py',
   'PYMODULE'),
  ('encryption.machine_code',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\machine_code.py',
   'PYMODULE'),
  ('encryption.register_window',
   'e:\\Software\\009automation\\violation\\违章查询_new\\encryption\\register_window.py',
   'PYMODULE'),
  ('et_xmlfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fontTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\__init__.py',
   'PYMODULE'),
  ('fontTools.agl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\agl.py',
   'PYMODULE'),
  ('fontTools.cffLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\cffLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFF2ToCFF',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\cffLib\\CFF2ToCFF.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFFToCFF2',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\cffLib\\CFFToCFF2.py',
   'PYMODULE'),
  ('fontTools.cffLib.specializer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\cffLib\\specializer.py',
   'PYMODULE'),
  ('fontTools.cffLib.transforms',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\cffLib\\transforms.py',
   'PYMODULE'),
  ('fontTools.cffLib.width',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\cffLib\\width.py',
   'PYMODULE'),
  ('fontTools.colorLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\colorLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.colorLib.builder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\colorLib\\builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.errors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\colorLib\\errors.py',
   'PYMODULE'),
  ('fontTools.colorLib.geometry',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\colorLib\\geometry.py',
   'PYMODULE'),
  ('fontTools.colorLib.table_builder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\colorLib\\table_builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.unbuilder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\colorLib\\unbuilder.py',
   'PYMODULE'),
  ('fontTools.config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\config\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\designspaceLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.split',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\designspaceLib\\split.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.statNames',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\designspaceLib\\statNames.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.types',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\designspaceLib\\types.py',
   'PYMODULE'),
  ('fontTools.encodings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\encodings\\__init__.py',
   'PYMODULE'),
  ('fontTools.encodings.StandardEncoding',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\encodings\\StandardEncoding.py',
   'PYMODULE'),
  ('fontTools.encodings.codecs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\encodings\\codecs.py',
   'PYMODULE'),
  ('fontTools.feaLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\feaLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.ast',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\feaLib\\ast.py',
   'PYMODULE'),
  ('fontTools.feaLib.error',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\feaLib\\error.py',
   'PYMODULE'),
  ('fontTools.feaLib.location',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\feaLib\\location.py',
   'PYMODULE'),
  ('fontTools.feaLib.lookupDebugInfo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\feaLib\\lookupDebugInfo.py',
   'PYMODULE'),
  ('fontTools.misc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.arrayTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\arrayTools.py',
   'PYMODULE'),
  ('fontTools.misc.classifyTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\classifyTools.py',
   'PYMODULE'),
  ('fontTools.misc.cliTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\cliTools.py',
   'PYMODULE'),
  ('fontTools.misc.configTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\configTools.py',
   'PYMODULE'),
  ('fontTools.misc.cython',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\cython.py',
   'PYMODULE'),
  ('fontTools.misc.dictTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\dictTools.py',
   'PYMODULE'),
  ('fontTools.misc.encodingTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\encodingTools.py',
   'PYMODULE'),
  ('fontTools.misc.etree',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\etree.py',
   'PYMODULE'),
  ('fontTools.misc.filenames',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\filenames.py',
   'PYMODULE'),
  ('fontTools.misc.fixedTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\fixedTools.py',
   'PYMODULE'),
  ('fontTools.misc.intTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\intTools.py',
   'PYMODULE'),
  ('fontTools.misc.iterTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\iterTools.py',
   'PYMODULE'),
  ('fontTools.misc.lazyTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\lazyTools.py',
   'PYMODULE'),
  ('fontTools.misc.loggingTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\loggingTools.py',
   'PYMODULE'),
  ('fontTools.misc.macCreatorType',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\macCreatorType.py',
   'PYMODULE'),
  ('fontTools.misc.macRes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\macRes.py',
   'PYMODULE'),
  ('fontTools.misc.plistlib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\plistlib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.psCharStrings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\psCharStrings.py',
   'PYMODULE'),
  ('fontTools.misc.roundTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\roundTools.py',
   'PYMODULE'),
  ('fontTools.misc.sstruct',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\sstruct.py',
   'PYMODULE'),
  ('fontTools.misc.textTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\textTools.py',
   'PYMODULE'),
  ('fontTools.misc.timeTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\timeTools.py',
   'PYMODULE'),
  ('fontTools.misc.transform',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\transform.py',
   'PYMODULE'),
  ('fontTools.misc.treeTools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\treeTools.py',
   'PYMODULE'),
  ('fontTools.misc.vector',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\vector.py',
   'PYMODULE'),
  ('fontTools.misc.visitor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\visitor.py',
   'PYMODULE'),
  ('fontTools.misc.xmlReader',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\xmlReader.py',
   'PYMODULE'),
  ('fontTools.misc.xmlWriter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\misc\\xmlWriter.py',
   'PYMODULE'),
  ('fontTools.otlLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\otlLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.builder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\otlLib\\builder.py',
   'PYMODULE'),
  ('fontTools.otlLib.error',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\otlLib\\error.py',
   'PYMODULE'),
  ('fontTools.otlLib.maxContextCalc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\otlLib\\maxContextCalc.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\otlLib\\optimize\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize.gpos',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\otlLib\\optimize\\gpos.py',
   'PYMODULE'),
  ('fontTools.pens',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\__init__.py',
   'PYMODULE'),
  ('fontTools.pens.basePen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\basePen.py',
   'PYMODULE'),
  ('fontTools.pens.boundsPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\boundsPen.py',
   'PYMODULE'),
  ('fontTools.pens.filterPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\filterPen.py',
   'PYMODULE'),
  ('fontTools.pens.pointPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\pointPen.py',
   'PYMODULE'),
  ('fontTools.pens.recordingPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\recordingPen.py',
   'PYMODULE'),
  ('fontTools.pens.reverseContourPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\reverseContourPen.py',
   'PYMODULE'),
  ('fontTools.pens.t2CharStringPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\t2CharStringPen.py',
   'PYMODULE'),
  ('fontTools.pens.transformPen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\pens\\transformPen.py',
   'PYMODULE'),
  ('fontTools.subset',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\subset\\__init__.py',
   'PYMODULE'),
  ('fontTools.subset.cff',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\subset\\cff.py',
   'PYMODULE'),
  ('fontTools.subset.svg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\subset\\svg.py',
   'PYMODULE'),
  ('fontTools.subset.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\subset\\util.py',
   'PYMODULE'),
  ('fontTools.ttLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.macUtils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\macUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.reorderGlyphs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\reorderGlyphs.py',
   'PYMODULE'),
  ('fontTools.ttLib.sfnt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\sfnt.py',
   'PYMODULE'),
  ('fontTools.ttLib.standardGlyphOrder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\standardGlyphOrder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.B_A_S_E_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\B_A_S_E_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.BitmapGlyphMetrics',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\BitmapGlyphMetrics.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_D_T_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_L_C_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F__2',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_O_L_R_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\C_O_L_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_P_A_L_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\C_P_A_L_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D_S_I_G_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\D_S_I_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D__e_b_g',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\D__e_b_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.DefaultTable',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\DefaultTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_D_T_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_L_C_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F_F_T_M_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\F_F_T_M_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F__e_a_t',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\F__e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_D_E_F_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G_D_E_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_M_A_P_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G_M_A_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_K_G_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_K_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_O_S_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_O_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_S_U_B_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G_S_U_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_V_A_R_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_a_t',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_o_c',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_o_c.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.H_V_A_R_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\H_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.J_S_T_F_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\J_S_T_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.L_T_S_H_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\L_T_S_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_A_T_H_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\M_A_T_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_E_T_A_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\M_E_T_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_V_A_R_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\M_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.O_S_2f_2',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\O_S_2f_2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_I_N_G_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\S_I_N_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_T_A_T_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\S_T_A_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_V_G_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\S_V_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_f',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_l',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_l.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_B_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_C_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_D_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_D_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_J_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_J_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_P_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_S_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_V_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_V_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__0',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__0.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__1',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__1.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__2',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__3',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__3.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__5',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__5.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_T_F_A_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\T_T_F_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.TupleVariation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\TupleVariation.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_A_R_C_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\V_A_R_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_D_M_X_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\V_D_M_X_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_O_R_G_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\V_O_R_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_V_A_R_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\V_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_n_k_r',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_a_n_k_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_v_a_r',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_a_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._b_s_l_n',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_b_s_l_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_i_d_g',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_c_i_d_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_m_a_p',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_c_m_a_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_a_r',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_t',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_e_a_t',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_f_e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_p_g_m',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_f_p_g_m.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_v_a_r',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_f_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_a_s_p',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_g_a_s_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_c_i_d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_g_c_i_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_l_y_f',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_g_l_y_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_v_a_r',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_g_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_d_m_x',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_h_d_m_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_e_a_d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_h_e_a_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_h_e_a',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_h_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_m_t_x',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_h_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._k_e_r_n',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_k_e_r_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_c_a_r',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_l_c_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_o_c_a',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_l_o_c_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_t_a_g',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_l_t_a_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_a_x_p',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_m_a_x_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_e_t_a',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_m_e_t_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_t',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_x',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._n_a_m_e',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_n_a_m_e.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._o_p_b_d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_o_p_b_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_o_s_t',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_p_o_s_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_e_p',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_e_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_o_p',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_o_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._s_b_i_x',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_s_b_i_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._t_r_a_k',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_t_r_a_k.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_h_e_a',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_v_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_m_t_x',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\_v_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.asciiTable',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\asciiTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.grUtils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\grUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otBase',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\otBase.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otConverters',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\otConverters.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otData',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\otData.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTables',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\otTables.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTraverse',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\otTraverse.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixGlyph',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\sbixGlyph.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixStrike',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\sbixStrike.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.ttProgram',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\tables\\ttProgram.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttCollection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\ttCollection.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttFont',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\ttFont.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttGlyphSet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\ttGlyphSet.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttVisitor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\ttVisitor.py',
   'PYMODULE'),
  ('fontTools.ttLib.woff2',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttLib\\woff2.py',
   'PYMODULE'),
  ('fontTools.ttx',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\ttx.py',
   'PYMODULE'),
  ('fontTools.unicode',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicode.py',
   'PYMODULE'),
  ('fontTools.unicodedata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicodedata\\__init__.py',
   'PYMODULE'),
  ('fontTools.unicodedata.Blocks',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicodedata\\Blocks.py',
   'PYMODULE'),
  ('fontTools.unicodedata.Mirrored',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicodedata\\Mirrored.py',
   'PYMODULE'),
  ('fontTools.unicodedata.OTTags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicodedata\\OTTags.py',
   'PYMODULE'),
  ('fontTools.unicodedata.ScriptExtensions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicodedata\\ScriptExtensions.py',
   'PYMODULE'),
  ('fontTools.unicodedata.Scripts',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\unicodedata\\Scripts.py',
   'PYMODULE'),
  ('fontTools.varLib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.varLib.builder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\builder.py',
   'PYMODULE'),
  ('fontTools.varLib.cff',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\cff.py',
   'PYMODULE'),
  ('fontTools.varLib.errors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\errors.py',
   'PYMODULE'),
  ('fontTools.varLib.featureVars',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\featureVars.py',
   'PYMODULE'),
  ('fontTools.varLib.merger',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\merger.py',
   'PYMODULE'),
  ('fontTools.varLib.models',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\models.py',
   'PYMODULE'),
  ('fontTools.varLib.multiVarStore',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\multiVarStore.py',
   'PYMODULE'),
  ('fontTools.varLib.mvar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\mvar.py',
   'PYMODULE'),
  ('fontTools.varLib.stat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\stat.py',
   'PYMODULE'),
  ('fontTools.varLib.varStore',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\fontTools\\varLib\\varStore.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\lzma.py',
   'PYMODULE'),
  ('matplotlib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._type1font',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_type1font.py',
   'PYMODULE'),
  ('matplotlib._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_pdf_ps',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_pdf_ps.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_mixed',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_mixed.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_pdf',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_pdf.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('resource', '-', 'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py',
   'PYMODULE'),
  ('timeit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\timeit.py',
   'PYMODULE'),
  ('tkcalendar',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tkcalendar\\__init__.py',
   'PYMODULE'),
  ('tkcalendar.calendar_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tkcalendar\\calendar_.py',
   'PYMODULE'),
  ('tkcalendar.dateentry',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tkcalendar\\dateentry.py',
   'PYMODULE'),
  ('tkcalendar.tooltip',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tkcalendar\\tooltip.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\typing.py',
   'PYMODULE'),
  ('tzdata',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('ui',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\__init__.py',
   'PYMODULE'),
  ('ui.app',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\app.py',
   'PYMODULE'),
  ('ui.email_settings_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\email_settings_frame.py',
   'PYMODULE'),
  ('ui.failed_query_dialog',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\failed_query_dialog.py',
   'PYMODULE'),
  ('ui.password_dialog',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\password_dialog.py',
   'PYMODULE'),
  ('ui.query_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\query_frame.py',
   'PYMODULE'),
  ('ui.results_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\results_frame.py',
   'PYMODULE'),
  ('ui.statistics_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\statistics_frame.py',
   'PYMODULE'),
  ('ui.vehicle_management_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\vehicle_management_frame.py',
   'PYMODULE'),
  ('ui.vehicle_selector_dialog',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\vehicle_selector_dialog.py',
   'PYMODULE'),
  ('ui.violation_transfer_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\violation_transfer_frame.py',
   'PYMODULE'),
  ('ui.wechat_assistant_frame',
   'e:\\Software\\009automation\\violation\\违章查询_new\\ui\\wechat_assistant_frame.py',
   'PYMODULE'),
  ('uiautomation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\uiautomation\\__init__.py',
   'PYMODULE'),
  ('uiautomation.uiautomation',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\uiautomation\\uiautomation.py',
   'PYMODULE'),
  ('uiautomation.version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\uiautomation\\version.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\utils\\__init__.py',
   'PYMODULE'),
  ('utils.config_manager',
   'e:\\Software\\009automation\\violation\\违章查询_new\\utils\\config_manager.py',
   'PYMODULE'),
  ('utils.constants',
   'e:\\Software\\009automation\\violation\\违章查询_new\\utils\\constants.py',
   'PYMODULE'),
  ('utils.date_utils',
   'e:\\Software\\009automation\\violation\\违章查询_new\\utils\\date_utils.py',
   'PYMODULE'),
  ('utils.email_config',
   'e:\\Software\\009automation\\violation\\违章查询_new\\utils\\email_config.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('win32com',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.universal',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wmi',
   'e:\\Software\\009automation\\violation\\违章查询_new\\venv\\Lib\\site-packages\\wmi.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
