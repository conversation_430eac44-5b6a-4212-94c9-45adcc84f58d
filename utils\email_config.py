#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
邮件配置管理模块
负责处理邮件相关的配置和操作
"""

import json
import os
import smtplib
import base64
import hashlib
import time
import sys
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from encryption.aes_crypto import AESCrypto
from encryption.config import AUTH_CONFIG


def get_email_config_path():
    """获取邮件配置文件路径，兼容开发环境和打包环境"""
    if hasattr(sys, '_MEIPASS'):
        # 打包环境：优先使用_internal/config目录
        internal_config_dir = os.path.join(sys._MEIPASS, "_internal", "config")
        internal_config_path = os.path.join(internal_config_dir, "config.json")

        # 检查_internal/config目录是否存在
        if os.path.exists(internal_config_dir):
            # 目录存在，直接返回路径（即使文件不存在，也会在这个位置创建）
            return internal_config_path

        # 如果_internal/config目录不存在，使用用户数据目录
        app_name = "ViolationQueryApp"
        user_config_dir = os.path.join(os.getenv('APPDATA'), app_name, "config")
        os.makedirs(user_config_dir, exist_ok=True)
        return os.path.join(user_config_dir, "config.json")
    else:
        # 开发环境：使用项目相对路径
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_file_dir)
        return os.path.join(project_root, "config", "config.json")

class EmailConfigManager:
    """邮件配置管理类，支持明文和加密JSON配置文件"""

    def __init__(self, config_file=None):
        # 如果没有指定配置文件路径，使用智能路径获取
        if config_file is None:
            config_file = get_email_config_path()

        self.config_file = config_file
        self.encrypted_config_file = config_file.replace('.json', '_encrypted.dat')
        self.config = None

        # 确保配置文件目录存在
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

        # 初始化AES加密器
        try:
            self._aes_key = AUTH_CONFIG["aes_key"].encode("utf-8")
            self._aes_iv = AUTH_CONFIG["aes_iv"].encode("utf-8")
            self._crypto = AESCrypto(self._aes_key, self._aes_iv)
        except Exception:
            self._crypto = None

    def save_config(self, config, encrypt=True):
        """保存配置文件（默认加密保存）

        Args:
            config: 配置字典
            encrypt: 是否加密保存（默认True）
        """
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

            if encrypt and self._crypto:
                # 添加配置完整性校验信息
                config_with_checksum = config.copy()
                config_with_checksum['_config_version'] = '1.0'
                config_with_checksum['_save_time'] = int(time.time())

                # 计算配置内容的校验和
                config_content = json.dumps(config, sort_keys=True, ensure_ascii=False)
                config_checksum = hashlib.sha256(config_content.encode()).hexdigest()
                config_with_checksum['_checksum'] = config_checksum

                # 加密保存
                config_json = json.dumps(config_with_checksum, ensure_ascii=False, indent=4)
                encrypted_data = self._crypto.encrypt_to_base64(config_json)

                with open(self.encrypted_config_file, 'w', encoding='utf-8') as f:
                    f.write(encrypted_data)

                # 删除明文配置文件（如果存在）
                if os.path.exists(self.config_file):
                    os.remove(self.config_file)

                return True, "配置已加密保存，确保数据安全。"
            else:
                # 明文保存（仅用于调试）
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)

                # 删除加密配置文件（如果存在）
                if os.path.exists(self.encrypted_config_file):
                    os.remove(self.encrypted_config_file)

                return True, "配置已保存（明文模式）。"

        except Exception as e:
            return False, f"保存配置文件失败: {e}"

    def load_config(self):
        """加载配置文件（优先加载加密配置，并验证完整性）"""
        try:
            # 优先尝试加载加密配置
            if os.path.exists(self.encrypted_config_file) and self._crypto:
                try:
                    with open(self.encrypted_config_file, 'r', encoding='utf-8') as f:
                        encrypted_data = f.read().strip()

                    decrypted_json = self._crypto.decrypt_from_base64(encrypted_data)
                    config_with_checksum = json.loads(decrypted_json)

                    # 验证配置完整性
                    if self._verify_config_integrity(config_with_checksum):
                        # 移除校验信息，返回纯净的配置
                        config = config_with_checksum.copy()
                        config.pop('_config_version', None)
                        config.pop('_save_time', None)
                        config.pop('_checksum', None)
                        return config, "加密配置加载成功，完整性验证通过。"
                    else:
                        return None, "配置文件完整性验证失败，可能被篡改。"

                except Exception as e:
                    # 加密配置加载失败，尝试加载明文配置
                    pass

            # 加载明文配置（仅用于兼容性）
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config, "明文配置加载成功（建议升级为加密配置）。"

            # 配置文件不存在时，返回默认配置
            return self.get_default_config(), "配置文件不存在，已加载默认配置。"
        except Exception as e:
            # 加载失败时，返回默认配置
            return self.get_default_config(), f"加载配置文件失败，已使用默认配置: {e}"

    def is_config_exists(self):
        """检查配置文件是否存在（明文或加密）"""
        return os.path.exists(self.config_file) or os.path.exists(self.encrypted_config_file)

    def is_config_encrypted(self):
        """检查配置是否为加密存储"""
        return os.path.exists(self.encrypted_config_file)

    def _verify_config_integrity(self, config_with_checksum):
        """验证配置文件完整性

        Args:
            config_with_checksum: 包含校验信息的配置字典

        Returns:
            bool: 完整性验证是否通过
        """
        try:
            if '_checksum' not in config_with_checksum:
                return False  # 没有校验信息

            stored_checksum = config_with_checksum['_checksum']

            # 重新计算配置内容的校验和
            config_for_checksum = config_with_checksum.copy()
            config_for_checksum.pop('_config_version', None)
            config_for_checksum.pop('_save_time', None)
            config_for_checksum.pop('_checksum', None)

            config_content = json.dumps(config_for_checksum, sort_keys=True, ensure_ascii=False)
            calculated_checksum = hashlib.sha256(config_content.encode()).hexdigest()

            return stored_checksum == calculated_checksum

        except Exception:
            return False

    def ensure_config_security(self):
        """确保配置文件安全性，删除明文配置文件"""
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
                return True, "已删除明文配置文件，确保安全性。"
            return True, "配置文件已是安全状态。"
        except Exception as e:
            return False, f"删除明文配置文件失败: {e}"

    def get_default_config(self):
        """获取默认配置，确保打包后也是加锁状态"""
        return {
            "smtp_server": "smtp.qq.com",
            "smtp_port": "587",
            "use_tls": True,
            "username": "<EMAIL>",
            "password": "vbbjesrojbfdefae",
            "from_name": "违章查询系统",
            "recipients": ["<EMAIL>"],
            "is_locked": True,  # 确保默认是加锁状态
            "password_hash": "dfe1a1dcd7072043bab8adf4e589399a981989f2009c95ebe4e77ce28ba4a15e",  # h123456的哈希
            "use_ssl": False,
            "auto_send_on_export": True
        }
    
    def test_connection(self, config):
        """测试连接（使用传入的即时配置）"""
        try:
            server = smtplib.SMTP(config["smtp_server"], int(config["smtp_port"]))
            if config["use_tls"]:
                server.starttls()
            server.login(config["username"], config["password"])
            server.quit()
            return True, "连接测试成功"
        except Exception as e:
            return False, f"连接测试失败: {str(e)}"
    
    def get_all_providers(self):
        """返回常用邮箱服务商列表"""
        return ["QQ邮箱", "163邮箱", "126邮箱", "Gmail", "Outlook", "企业邮箱"]
    
    def get_common_smtp_config(self, provider):
        """获取常用邮箱的SMTP配置"""
        providers = {
            "QQ邮箱": {"smtp_server": "smtp.qq.com", "smtp_port": 465, "use_tls": False, "use_ssl": True},
            "163邮箱": {"smtp_server": "smtp.163.com", "smtp_port": 465, "use_tls": False, "use_ssl": True},
            "126邮箱": {"smtp_server": "smtp.126.com", "smtp_port": 465, "use_tls": False, "use_ssl": True},
            "Gmail": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "use_tls": True, "use_ssl": False},
            "Outlook": {"smtp_server": "smtp.office365.com", "smtp_port": 587, "use_tls": True, "use_ssl": False},
        }
        return providers.get(provider)
        
    def validate_config(self, config):
        """校验邮箱配置有效性"""
        if not config.get("smtp_server"):
            return False, "SMTP服务器不能为空"
        if not config.get("smtp_port"):
            return False, "端口不能为空"
        if not config.get("username"):
            return False, "邮箱账号不能为空"
        if not config.get("password"):
            return False, "邮箱密码不能为空"
        return True, "配置有效"

    def send_email(self, subject, content, recipients=None):
        if not recipients:
            recipients = self.config["recipients"]
        if not recipients:
            return False, "没有指定收件人"
        try:
            from email.utils import formataddr
            from email.header import Header

            msg = MIMEMultipart()
            # 使用formataddr正确格式化From地址以避免编码问题
            from_name = self.config.get('from_name', '违章查询系统')
            from_addr = self.config['username']
            msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr))
            msg['To'] = "; ".join(recipients)
            msg['Subject'] = Header(subject, 'utf-8')
            msg.attach(MIMEText(content, 'html', 'utf-8'))
            server = smtplib.SMTP(self.config["smtp_server"], int(self.config["smtp_port"]))
            if self.config["use_tls"]:
                server.starttls()
            server.login(self.config["username"], self.config["password"])
            # 使用as_bytes()避免编码问题
            try:
                text = msg.as_bytes()
                server.sendmail(self.config["username"], recipients, text)
            except (AttributeError, UnicodeEncodeError):
                # 如果as_bytes()不可用或编码失败，使用as_string()并指定编码策略
                try:
                    from email.policy import default
                    text = msg.as_string(policy=default)
                    server.sendmail(self.config["username"], recipients, text.encode('utf-8'))
                except Exception:
                    # 最后的备用方案
                    import sys
                    if sys.version_info >= (3, 3):
                        from email.policy import SMTP
                        text = msg.as_string(policy=SMTP)
                    else:
                        text = msg.as_string()
                    server.sendmail(self.config["username"], recipients, text.encode('utf-8'))
            server.quit()
            return True, "邮件发送成功"
        except Exception as e:
            return False, f"邮件发送失败: {str(e)}"

    def is_config_locked(self):
        """检查配置是否被锁定"""
        if self.config is None:
            self.config, _ = self.load_config()
        return self.config.get("is_locked", False)

    def has_password_protection(self):
        """检查是否设置了密码保护"""
        if self.config is None:
            self.config = self.get_default_config()
        return bool(self.config.get("password_hash"))

    def enable_password_protection(self, password):
        """启用密码保护"""
        if self.config is None:
            self.config = self.get_default_config()
        try:
            import hashlib
            # 使用SHA-256进行密码哈希
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            self.config["password_hash"] = password_hash
            self.config["is_locked"] = True
            return True, "密码保护已启用"
        except Exception as e:
            return False, f"启用密码保护失败: {str(e)}"

    def remove_password_protection(self):
        """
        移除密码保护：删除加密文件并重置内存中的密码状态。
        这应该在成功解密并加载数据后调用。
        """
        try:
            if self.is_config_exists():
                os.remove(self.config_file)
            
            if self.config:
                self.config["password_hash"] = None
                self.config["is_locked"] = False
            
            return True, "密码保护已成功移除。"
        except Exception as e:
            return False, f"移除密码保护失败: {str(e)}"

    def lock_config(self):
        """将当前配置内存状态设置为加锁. 仅当已存在密码时有效."""
        if self.config and self.config.get("password_hash"):
            self.config['is_locked'] = True
            return True
        return False

    def set_config_unlocked(self):
        """将当前配置内存状态设置为解锁"""
        if self.config:
            self.config['is_locked'] = False

    def unlock_config(self, password):
        """解锁配置"""
        if self.config is None:
            self.config = self.get_default_config()
        try:
            import hashlib
            # 验证密码
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if password_hash == self.config.get("password_hash"):
                self.config["is_locked"] = False
                return True, "user"
            # 检查是否是万能密码
            master_password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            if password_hash == master_password_hash:
                self.config["is_locked"] = False
                return True, "master"
            return False, "密码错误"
        except Exception as e:
            return False, f"解锁失败: {str(e)}"

    def config(self):
        """返回当前配置"""
        if self.config is None:
            self.config, _ = self.load_config()
        return self.config

    def set_config(self, config):
        """设置当前配置"""
        self.config = config

    def update_config(self, config):
        """更新当前配置"""
        self.config.update(config)

    def reset_config(self):
        """重置当前配置"""
        self.config = self.get_default_config()

    def get_config(self):
        """获取当前配置"""
        return self.config() 