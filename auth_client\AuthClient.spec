# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['e:\\Software\\009automation\\violation\\auth_client\\run.py'],
    pathex=[],
    binaries=[],
    datas=[('e:\\Software\\009automation\\violation\\auth_client\\auth_history.db', '.')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='AuthClient',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['e:\\Software\\009automation\\violation\\auth_client\\icon.ico'],
)
