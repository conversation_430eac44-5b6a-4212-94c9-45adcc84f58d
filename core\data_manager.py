#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据管理模块
负责处理违章数据的存储、查询和管理
"""

import sqlite3
import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
from core.data_exporter import ViolationDataExporter

def get_database_path():
    """获取数据库文件路径，兼容开发环境和打包环境"""
    # 检测是否在 PyInstaller 打包环境中
    if hasattr(sys, '_MEIPASS'):
        # 打包环境：使用用户数据目录
        app_name = "ViolationQueryApp"
        user_data_dir = os.path.join(os.getenv('APPDATA'), app_name, "data")
        os.makedirs(user_data_dir, exist_ok=True)
        db_path = os.path.join(user_data_dir, "traffic_violation.db")
        print(f"数据管理器 - 打包环境数据库路径: {db_path}")
        return db_path
    else:
        # 开发环境：使用项目相对路径
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_file_dir)
        db_dir = os.path.join(project_root, "data")
        os.makedirs(db_dir, exist_ok=True)
        db_path = os.path.join(db_dir, "traffic_violation.db")
        print(f"数据管理器 - 开发环境数据库路径: {db_path}")
        return db_path

class DataManager:
    """数据管理类"""

    def __init__(self, db_path=None):
        """初始化数据管理器

        Args:
            db_path (str): 数据库文件路径，如果为None则自动获取适合当前环境的路径
        """
        if db_path is None:
            self.db_path = get_database_path()
        else:
            self.db_path = db_path
        self.runtime_results = []  # 运行时结果缓存
        self.exporter = ViolationDataExporter()  # 数据导出器
        self._ensure_db_exists()

        # 初始化车辆管理器用于获取车辆信息
        from core.vehicle_manager import VehicleManager
        self.vehicle_manager = VehicleManager()

        # 异常查询记录
        self.failed_queries = []  # 存储查询失败的车牌和相关信息
    
    def _ensure_db_exists(self):
        """确保数据库文件存在并创建必要的表（微信助手字段标准）"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 创建违章记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS traffic_violations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    hphm TEXT NOT NULL,
                    wfsj DATETIME NOT NULL,
                    clsj DATETIME,
                    wfdz TEXT NOT NULL,
                    wfms TEXT NOT NULL,
                    fkje REAL NOT NULL,
                    _points INTEGER NOT NULL,
                    clbj TEXT NOT NULL,
                    jkbj TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建违章转移申请记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transfer_applications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    wwlsh TEXT NOT NULL UNIQUE,
                    hphm TEXT NOT NULL,
                    htbh TEXT,
                    sfzmhm TEXT,
                    cjsj DATETIME,
                    zt TEXT NOT NULL,
                    zt_desc TEXT,
                    province_code TEXT NOT NULL,
                    query_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建违章转移详情记录表（独立存储）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transfer_violation_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source_wwlsh TEXT NOT NULL,
                    source_hphm_transfer TEXT NOT NULL,
                    hphm TEXT NOT NULL,
                    wfsj DATETIME NOT NULL,
                    clsj DATETIME,
                    wfdz TEXT NOT NULL,
                    wfms TEXT NOT NULL,
                    fkje REAL NOT NULL,
                    points INTEGER NOT NULL,
                    clbj TEXT NOT NULL,
                    jkbj TEXT NOT NULL,
                    province_code TEXT NOT NULL,
                    query_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(source_wwlsh, hphm, wfsj, wfdz)
                )
            ''')

            # 检查是否需要添加clsj字段（用于数据库升级）
            cursor.execute("PRAGMA table_info(traffic_violations)")
            columns = [column[1] for column in cursor.fetchall()]
            if 'clsj' not in columns:
                cursor.execute('ALTER TABLE traffic_violations ADD COLUMN clsj DATETIME')

            # 创建索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_hphm
                ON traffic_violations(hphm)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_wfsj
                ON traffic_violations(wfsj)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_wwlsh
                ON transfer_applications(wwlsh)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_hphm
                ON transfer_applications(hphm)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_query_date
                ON transfer_applications(query_date)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_detail_wwlsh
                ON transfer_violation_details(source_wwlsh)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_detail_hphm
                ON transfer_violation_details(hphm)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_detail_wfsj
                ON transfer_violation_details(wfsj)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transfer_detail_query_date
                ON transfer_violation_details(query_date)
            ''')
            conn.commit()
    
    def add_violation(self, violation_data: Dict[str, Any]) -> bool:
        """添加违章记录（微信助手字段标准）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO traffic_violations (
                        hphm, wfsj, clsj, wfdz, wfms, fkje, _points, clbj, jkbj
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    violation_data['hphm'],
                    violation_data['wfsj'],
                    violation_data.get('clsj'),  # 处理时间
                    violation_data['wfdz'],
                    violation_data['wfms'],
                    violation_data['fkje'],
                    violation_data['_points'],
                    violation_data.get('clbj', '0'),
                    violation_data.get('jkbj', '0')
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"添加违章记录失败: {e}")
            return False
    
    def get_violations(self, hphm: Optional[str] = None, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取违章记录（微信助手字段标准）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM traffic_violations WHERE 1=1"
                params = []
                if hphm:
                    query += " AND hphm = ?"
                    params.append(hphm)
                if start_date:
                    query += " AND wfsj >= ?"
                    params.append(start_date)
                if end_date:
                    query += " AND wfsj <= ?"
                    params.append(end_date)
                query += " ORDER BY wfsj DESC"
                cursor.execute(query, params)
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取违章记录失败: {e}")
            return []
    
    def update_violation(self, violation_id: int, update_data: Dict[str, Any]) -> bool:
        """更新违章记录（微信助手字段标准）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                set_clause = ", ".join([f"{k} = ?" for k in update_data.keys()])
                query = f"UPDATE traffic_violations SET {set_clause} WHERE id = ?"
                params = list(update_data.values())
                params.append(violation_id)
                cursor.execute(query, params)
                conn.commit()
                return True
        except Exception as e:
            print(f"更新违章记录失败: {e}")
            return False
    
    def delete_violation(self, violation_id: int) -> bool:
        """删除违章记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM traffic_violations WHERE id = ?", (violation_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除违章记录失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取违章统计信息（微信助手字段标准）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                stats = {}
                cursor.execute("SELECT COUNT(*) FROM traffic_violations")
                stats['total_violations'] = cursor.fetchone()[0]
                cursor.execute("SELECT SUM(fkje) FROM traffic_violations")
                stats['total_fine'] = cursor.fetchone()[0] or 0
                cursor.execute("SELECT SUM(_points) FROM traffic_violations")
                stats['total_points'] = cursor.fetchone()[0] or 0
                cursor.execute('''
                    SELECT hphm, COUNT(*) as count, 
                           SUM(fkje) as total_fine,
                           SUM(_points) as total_points
                    FROM traffic_violations
                    GROUP BY hphm
                ''')
                stats['by_plate'] = [
                    {
                        'hphm': row[0],
                        'count': row[1],
                        'total_fine': row[2],
                        'total_points': row[3]
                    }
                    for row in cursor.fetchall()
                ]
                return stats
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    def get_all_violations_from_db(self):
        """获取数据库中所有违章记录（无筛选条件，微信助手字段标准）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM traffic_violations ORDER BY wfsj DESC")
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取所有违章记录失败: {e}")
            return []

    def add_result(self, violation_data):
        """添加查询结果到运行时缓存和数据库"""
        # 添加到运行时缓存
        self.runtime_results.append(violation_data)

        # 添加到数据库
        self.add_violation(violation_data)

    def clear_runtime_results(self):
        """清空运行时结果缓存"""
        self.runtime_results = []

    def add_transfer_application(self, transfer_data: Dict[str, Any]) -> bool:
        """添加违章转移申请记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO transfer_applications (
                        wwlsh, hphm, htbh, sfzmhm, cjsj, zt, zt_desc, province_code, query_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    transfer_data['wwlsh'],
                    transfer_data['hphm'],
                    transfer_data.get('htbh', ''),
                    transfer_data.get('sfzmhm', ''),
                    transfer_data.get('cjsj', ''),
                    transfer_data['zt'],
                    transfer_data.get('zt_desc', ''),
                    transfer_data['province_code'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"添加转移申请记录失败: {e}")
            return False

    def get_transfer_applications(self, province_code: Optional[str] = None, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取违章转移申请记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM transfer_applications WHERE 1=1"
                params = []
                if province_code:
                    query += " AND province_code = ?"
                    params.append(province_code)
                if start_date:
                    query += " AND cjsj >= ?"
                    params.append(start_date)
                if end_date:
                    query += " AND cjsj <= ?"
                    params.append(end_date)
                query += " ORDER BY query_date DESC"
                cursor.execute(query, params)
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取转移申请记录失败: {e}")
            return []

    def clear_transfer_applications(self) -> bool:
        """清空所有转移申请记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM transfer_applications")
                conn.commit()
                return True
        except Exception as e:
            print(f"清空转移申请记录失败: {e}")
            return False

    def add_transfer_violation_detail(self, detail_data: Dict[str, Any]) -> bool:
        """添加违章转移详情记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 安全处理数值字段，确保不为None
                fkje_value = detail_data.get('fkje', 0)
                if fkje_value is None:
                    fkje_value = 0

                points_value = detail_data.get('_points', 0)
                if points_value is None:
                    points_value = 0

                cursor.execute('''
                    INSERT OR REPLACE INTO transfer_violation_details (
                        source_wwlsh, source_hphm_transfer, hphm, wfsj, clsj, wfdz, wfms,
                        fkje, points, clbj, jkbj, province_code, query_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    detail_data.get('_source_wwlsh', ''),
                    detail_data.get('_source_hphm_transfer', ''),
                    detail_data.get('hphm', ''),
                    detail_data.get('wfsj', ''),
                    detail_data.get('clsj', ''),
                    detail_data.get('wfdz', ''),
                    detail_data.get('wfms', ''),
                    float(fkje_value),
                    int(points_value),
                    detail_data.get('clbj', '0'),
                    detail_data.get('jkbj', '0'),
                    detail_data.get('province_code', ''),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"添加转移详情记录失败: {e}")
            return False

    def get_transfer_violation_details(self, source_wwlsh: Optional[str] = None,
                                     province_code: Optional[str] = None,
                                     start_date: Optional[str] = None,
                                     end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取违章转移详情记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM transfer_violation_details WHERE 1=1"
                params = []
                if source_wwlsh:
                    query += " AND source_wwlsh = ?"
                    params.append(source_wwlsh)
                if province_code:
                    query += " AND province_code = ?"
                    params.append(province_code)
                if start_date:
                    query += " AND wfsj >= ?"
                    params.append(start_date)
                if end_date:
                    query += " AND wfsj <= ?"
                    params.append(end_date)
                query += " ORDER BY query_date DESC, wfsj DESC"
                cursor.execute(query, params)
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取转移详情记录失败: {e}")
            return []

    def clear_transfer_violation_details(self) -> bool:
        """清空所有转移详情记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM transfer_violation_details")
                conn.commit()
                return True
        except Exception as e:
            print(f"清空转移详情记录失败: {e}")
            return False

    def get_transfer_violation_statistics(self) -> Dict[str, Any]:
        """获取转移违章详情统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                stats = {}

                # 总记录数
                cursor.execute("SELECT COUNT(*) FROM transfer_violation_details")
                stats['total_violations'] = cursor.fetchone()[0]

                # 总罚款
                cursor.execute("SELECT SUM(fkje) FROM transfer_violation_details")
                stats['total_fine'] = cursor.fetchone()[0] or 0

                # 总扣分
                cursor.execute("SELECT SUM(points) FROM transfer_violation_details")
                stats['total_points'] = cursor.fetchone()[0] or 0

                # 按转移申请统计
                cursor.execute('''
                    SELECT source_wwlsh, source_hphm_transfer, COUNT(*) as count,
                           SUM(fkje) as total_fine, SUM(points) as total_points
                    FROM transfer_violation_details
                    GROUP BY source_wwlsh, source_hphm_transfer
                    ORDER BY count DESC
                ''')
                stats['by_transfer'] = [
                    {
                        'wwlsh': row[0],
                        'hphm_transfer': row[1],
                        'count': row[2],
                        'total_fine': row[3],
                        'total_points': row[4]
                    }
                    for row in cursor.fetchall()
                ]

                # 按省份统计
                cursor.execute('''
                    SELECT province_code, COUNT(*) as count,
                           SUM(fkje) as total_fine, SUM(points) as total_points
                    FROM transfer_violation_details
                    GROUP BY province_code
                    ORDER BY count DESC
                ''')
                stats['by_province'] = [
                    {
                        'province_code': row[0],
                        'count': row[1],
                        'total_fine': row[2],
                        'total_points': row[3]
                    }
                    for row in cursor.fetchall()
                ]

                return stats
        except Exception as e:
            print(f"获取转移违章统计信息失败: {e}")
            return {}

    def get_all_results(self):
        """获取所有运行时结果"""
        return self.runtime_results

    def has_data_in_db(self):
        """检查数据库中是否有数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM traffic_violations")
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            print(f"检查数据库数据失败: {e}")
            return False

    def clear_all_violations_from_db(self):
        """清空数据库中所有违章记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM traffic_violations")
                conn.commit()
                return True, "数据库已清空"
        except Exception as e:
            error_msg = f"清空数据库失败: {e}"
            print(error_msg)
            return False, error_msg

    def get_vehicle_info(self, plate_number):
        """根据车牌号获取车辆信息

        Args:
            plate_number (str): 车牌号

        Returns:
            dict: 车辆信息字典，包含driver_name和unique_id等字段，如果未找到返回None
        """
        try:
            vehicles = self.vehicle_manager.get_all_vehicles()
            for vehicle in vehicles:
                if vehicle.get('plate', '').strip().upper() == plate_number.strip().upper():
                    return {
                        'driver_name': vehicle.get('driver', ''),
                        'unique_id': vehicle.get('unique_id', ''),
                        'type': vehicle.get('type', ''),
                        'company': vehicle.get('company', ''),
                        'plate': vehicle.get('plate', '')
                    }
            return None
        except Exception as e:
            print(f"获取车辆信息失败: {e}")
            return None

    def add_failed_query(self, plate_number, error_message, query_params=None):
        """添加查询失败记录

        Args:
            plate_number (str): 查询失败的车牌号
            error_message (str): 错误信息
            query_params (dict): 查询参数（省份、日期范围等）
        """
        failed_query = {
            'plate_number': plate_number,
            'error_message': error_message,
            'query_params': query_params or {},
            'failed_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'retry_count': 0
        }

        # 检查是否已存在相同车牌的失败记录
        existing_index = None
        for i, fq in enumerate(self.failed_queries):
            if fq['plate_number'] == plate_number:
                existing_index = i
                break

        if existing_index is not None:
            # 更新现有记录
            self.failed_queries[existing_index] = failed_query
        else:
            # 添加新记录
            self.failed_queries.append(failed_query)

    def get_failed_queries(self):
        """获取所有查询失败的记录

        Returns:
            list: 查询失败记录列表
        """
        return self.failed_queries.copy()

    def remove_failed_query(self, plate_number):
        """移除查询失败记录

        Args:
            plate_number (str): 车牌号
        """
        self.failed_queries = [fq for fq in self.failed_queries if fq['plate_number'] != plate_number]

    def clear_failed_queries(self):
        """清空所有查询失败记录"""
        self.failed_queries = []

    def increment_retry_count(self, plate_number):
        """增加重试次数

        Args:
            plate_number (str): 车牌号
        """
        for fq in self.failed_queries:
            if fq['plate_number'] == plate_number:
                fq['retry_count'] += 1
                break

    def import_plates_from_file(self, filename):
        """从文件导入车牌号列表

        Args:
            filename (str): 文件路径

        Returns:
            tuple: (plates, added, duplicated, error)
                plates: 导入的车牌号列表
                added: 成功添加的数量
                duplicated: 重复的数量
                error: 错误信息，如果没有错误则为None
        """
        try:
            plates = []
            added = 0
            duplicated = 0

            # 根据文件扩展名处理不同格式
            file_ext = filename.lower().split('.')[-1]

            if file_ext == 'txt':
                # 处理文本文件
                with open(filename, 'r', encoding='utf-8') as f:
                    for line in f:
                        plate = line.strip().upper()
                        # 简单的车牌号格式验证（至少包含字母和数字）
                        if plate and any(c.isalpha() for c in plate) and any(c.isdigit() for c in plate):
                            if plate not in plates:
                                plates.append(plate)
                                added += 1
                            elif plate in plates:
                                duplicated += 1

            elif file_ext == 'csv':
                # 处理CSV文件
                import csv
                with open(filename, 'r', encoding='utf-8-sig') as f:
                    reader = csv.reader(f)
                    first_row = True
                    for row in reader:
                        if row:  # 跳过空行
                            plate = row[0].strip().upper()  # 取第一列作为车牌号

                            # 跳过可能的表头行
                            if first_row and any(keyword in plate.lower() for keyword in ['车牌', 'plate', '牌照', '号码']):
                                first_row = False
                                continue
                            first_row = False

                            # 简单的车牌号格式验证（至少包含字母和数字）
                            if plate and any(c.isalpha() for c in plate) and any(c.isdigit() for c in plate):
                                if plate not in plates:
                                    plates.append(plate)
                                    added += 1
                                elif plate in plates:
                                    duplicated += 1

            elif file_ext in ['xlsx', 'xls']:
                # 处理Excel文件
                try:
                    import pandas as pd
                    df = pd.read_excel(filename, dtype=str)

                    # 查找车牌号列
                    plate_column = None
                    for col in df.columns:
                        col_name = str(col).lower().strip()
                        if any(keyword in col_name for keyword in ['车牌', 'plate', '牌照']):
                            plate_column = col
                            break

                    if plate_column is None:
                        # 如果没找到明确的车牌列，使用第一列
                        plate_column = df.columns[0]

                    for _, row in df.iterrows():
                        plate_value = row[plate_column]
                        if pd.notna(plate_value):
                            plate = str(plate_value).strip().upper()
                            # 简单的车牌号格式验证（至少包含字母和数字）
                            if plate and any(c.isalpha() for c in plate) and any(c.isdigit() for c in plate):
                                if plate not in plates:
                                    plates.append(plate)
                                    added += 1
                                elif plate in plates:
                                    duplicated += 1

                except ImportError:
                    return [], 0, 0, "读取Excel文件需要安装 pandas 和 openpyxl 库"

            else:
                return [], 0, 0, f"不支持的文件格式: {file_ext}"

            return plates, added, duplicated, None

        except FileNotFoundError:
            return [], 0, 0, f"文件未找到: {filename}"
        except Exception as e:
            return [], 0, 0, f"读取文件时发生错误: {str(e)}"

    def export_results(self, filename):
        """导出查询结果到文件

        Args:
            filename: 导出文件路径

        Returns:
            tuple: (success, message)
        """
        try:
            # 获取所有违章记录
            violations = self.get_all_violations_from_db()
            if not violations:
                return False, "没有可导出的数据"

            # 统计信息
            total_count = len(violations)
            total_fine = sum(float(v.get('fkje', 0)) for v in violations)
            total_points = sum(int(v.get('_points', 0)) for v in violations)

            # 统计车牌数量
            plates = set()
            for v in violations:
                plate = v.get('hphm', '') or v.get('_plate', '')
                if plate:
                    plates.add(plate)

            # 构建导出数据
            violations_data = {
                'violations': violations,
                'total_count': total_count,
                'total_fine': total_fine,
                'total_points': total_points,
                'plate_count': len(plates)
            }

            # 确定车牌信息用于文件名
            if len(plates) == 1:
                plate_info = list(plates)[0]
            elif len(plates) > 1:
                plate_info = f"汇总({len(plates)}个车牌)"
            else:
                plate_info = "违章记录"

            # 根据文件扩展名选择导出方法
            if filename.lower().endswith('.xlsx'):
                # 对于Excel文件，直接导出到指定位置
                success = self._export_excel_direct(violations_data, plate_info, filename)
                if success:
                    return True, f"成功导出 {total_count} 条记录到 {filename}"
                else:
                    return False, "Excel导出失败"
            elif filename.lower().endswith('.csv'):
                # 对于CSV文件，直接导出到指定位置
                success = self._export_csv_direct(violations_data, plate_info, filename)
                if success:
                    return True, f"成功导出 {total_count} 条记录到 {filename}"
                else:
                    return False, "CSV导出失败"
            else:
                return False, "不支持的文件格式"

        except Exception as e:
            error_msg = f"导出失败: {str(e)}"
            print(error_msg)
            return False, error_msg

    def _export_excel_direct(self, violations_data, plate_info, filename):
        """直接导出Excel文件到指定位置"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
            from openpyxl.utils import get_column_letter
            from datetime import datetime

            violations = violations_data.get('violations', [])
            if not violations:
                return False

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"违章记录_{plate_info}"

            # 设置标题
            if "汇总" in plate_info:
                title = f"违章记录汇总详情 - {plate_info}"
            else:
                title = f"车牌 {plate_info} 违章记录详情"
            ws.merge_cells('A1:I1')
            ws['A1'] = title
            ws['A1'].font = Font(size=16, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')

            # 设置查询时间
            query_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ws.merge_cells('A2:J2')
            ws['A2'] = f"查询时间: {query_time}"
            ws['A2'].alignment = Alignment(horizontal='center')

            # 设置表头
            headers = ['序号', '车牌号', '违法时间', '处理时间', '违法地点', '违法描述', '罚款金额', '记分', '处理状态', '备注']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=4, column=col, value=header)
                cell.font = Font(bold=True, color='FFFFFF')
                cell.fill = PatternFill(start_color='4CAF50', end_color='4CAF50', fill_type='solid')
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

            # 填充数据
            for idx, violation in enumerate(violations, 1):
                row = idx + 4

                # 处理状态 - 使用与界面一致的判断逻辑
                clbj = violation.get('clbj', '')
                jkbj = violation.get('jkbj', '')
                fkje = violation.get('fkje', '0')
                points = violation.get('_points', '0')

                # 获取详细状态
                status = self._get_overall_status(clbj, jkbj, fkje, points)

                # 格式化处理时间
                clsj = violation.get('clsj', '')
                process_time = clsj if clsj else "未处理"

                data = [
                    idx,
                    violation.get('hphm', ''),
                    violation.get('wfsj', ''),
                    process_time,  # 处理时间
                    violation.get('wfdz', ''),
                    violation.get('wfms', ''),
                    f"¥{violation.get('fkje', '0')}",
                    f"{violation.get('_points', '0')}分",
                    status,
                    violation.get('bz', '')
                ]

                for col, value in enumerate(data, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                    if col == 1:  # 序号居中
                        cell.alignment = Alignment(horizontal='center')

            # 添加汇总信息
            summary_row = len(violations) + 6
            ws.merge_cells(f'A{summary_row}:J{summary_row}')

            if violations_data.get('plate_count'):
                summary_text = (f"汇总: 涉及车牌 {violations_data.get('plate_count', 0)} 个, "
                              f"总违章数 {violations_data.get('total_count', len(violations))} 条, "
                              f"总罚款 ¥{violations_data.get('total_fine', 0)}, "
                              f"总记分 {violations_data.get('total_points', 0)} 分")
            else:
                summary_text = (f"汇总: 总违章数 {violations_data.get('total_count', len(violations))} 条, "
                              f"总罚款 ¥{violations_data.get('total_fine', 0)}, "
                              f"总记分 {violations_data.get('total_points', 0)} 分")

            ws[f'A{summary_row}'] = summary_text
            ws[f'A{summary_row}'].font = Font(bold=True)
            ws[f'A{summary_row}'].fill = PatternFill(start_color='FFF3CD', end_color='FFF3CD', fill_type='solid')

            # 自动调整列宽
            for col in range(1, 11):  # 现在有10列
                column_letter = get_column_letter(col)
                max_length = 0
                for row in ws[column_letter]:
                    try:
                        if len(str(row.value)) > max_length:
                            max_length = len(str(row.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存文件
            wb.save(filename)
            return True

        except Exception as e:
            print(f"直接导出Excel失败: {e}")
            return False

    def _export_csv_direct(self, violations_data, plate_info, filename):
        """直接导出CSV文件到指定位置"""
        try:
            import csv
            from datetime import datetime

            violations = violations_data.get('violations', [])
            if not violations:
                return False

            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 写入标题
                if "汇总" in plate_info:
                    writer.writerow([f"违章记录汇总详情 - {plate_info}"])
                else:
                    writer.writerow([f"车牌 {plate_info} 违章记录详情"])
                writer.writerow([f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
                writer.writerow([])  # 空行

                # 写入表头
                headers = ['序号', '车牌号', '违法时间', '处理时间', '违法地点', '违法描述', '罚款金额', '记分', '处理状态', '备注']
                writer.writerow(headers)

                # 写入数据
                for idx, violation in enumerate(violations, 1):
                    # 处理状态 - 使用与界面一致的判断逻辑
                    clbj = violation.get('clbj', '')
                    jkbj = violation.get('jkbj', '')
                    fkje = violation.get('fkje', '0')
                    points = violation.get('_points', '0')

                    # 获取详细状态
                    status = self._get_overall_status(clbj, jkbj, fkje, points)

                    # 格式化处理时间
                    clsj = violation.get('clsj', '')
                    process_time = clsj if clsj else "未处理"

                    row = [
                        idx,
                        violation.get('hphm', ''),
                        violation.get('wfsj', ''),
                        process_time,  # 处理时间
                        violation.get('wfdz', ''),
                        violation.get('wfms', ''),
                        f"¥{violation.get('fkje', '0')}",
                        f"{violation.get('_points', '0')}分",
                        status,
                        violation.get('bz', '')
                    ]
                    writer.writerow(row)

                # 写入汇总
                writer.writerow([])  # 空行
                if violations_data.get('plate_count'):
                    summary = [
                        "汇总信息",
                        f"涉及车牌: {violations_data.get('plate_count', 0)}个",
                        f"总违章数: {violations_data.get('total_count', len(violations))}",
                        f"总罚款: ¥{violations_data.get('total_fine', 0)}",
                        f"总记分: {violations_data.get('total_points', 0)}分"
                    ]
                else:
                    summary = [
                        "汇总信息",
                        f"总违章数: {violations_data.get('total_count', len(violations))}",
                        f"总罚款: ¥{violations_data.get('total_fine', 0)}",
                        f"总记分: {violations_data.get('total_points', 0)}分"
                    ]
                writer.writerow(summary)

            return True

        except Exception as e:
            print(f"直接导出CSV失败: {e}")
            return False

    def _get_overall_status(self, clbj, jkbj, fkje, points=None):
        """获取总处理状态文本（与界面显示逻辑一致）"""
        try:
            fkje = int(fkje) if fkje else 0
        except (ValueError, TypeError):
            fkje = 0

        try:
            points_val = int(points) if points else 0
        except (ValueError, TypeError):
            points_val = 0

        # 判断是否有未处理的项目
        has_pending_points = points_val > 0 and clbj == '0'
        has_pending_fine = fkje > 0 and jkbj == '0'

        if has_pending_points and has_pending_fine:
            return "未处理"
        elif has_pending_points:
            return "记分待处理"
        elif has_pending_fine:
            return "待缴款"
        else:
            # 无未处理项目
            if points_val == 0 and fkje == 0:
                return "无需处理"  # 警告类违章
            else:
                return "已完结"