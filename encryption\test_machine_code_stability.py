#!/usr/bin/env python3
"""
机器码稳定性测试工具

用于测试和诊断机器码生成的稳定性问题
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from encryption.machine_code import MachineCodeGenerator

def test_machine_code_stability():
    """测试机器码稳定性"""
    print("=" * 80)
    print("机器码稳定性测试工具")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    generator = MachineCodeGenerator()
    
    # 1. 清除缓存，测试原始稳定性
    print("1. 清除缓存，测试原始硬件信息稳定性...")
    generator.clear_cache()
    
    codes_without_cache = []
    for i in range(5):
        code = generator.get_machine_code(use_cache=False)
        codes_without_cache.append(code)
        print(f"   第 {i+1} 次 (无缓存): {code}")
        time.sleep(0.5)  # 短暂延迟
    
    unique_without_cache = set(codes_without_cache)
    if len(unique_without_cache) == 1:
        print("   ✅ 硬件信息稳定")
    else:
        print(f"   ❌ 硬件信息不稳定，发现 {len(unique_without_cache)} 个不同的机器码")
    
    print()
    
    # 2. 测试缓存机制
    print("2. 测试缓存机制...")
    generator.clear_cache()
    
    # 第一次生成（会创建缓存）
    first_code = generator.get_machine_code(use_cache=True)
    print(f"   首次生成 (创建缓存): {first_code}")
    
    # 后续生成（使用缓存）
    codes_with_cache = []
    for i in range(3):
        code = generator.get_machine_code(use_cache=True)
        codes_with_cache.append(code)
        print(f"   第 {i+1} 次 (使用缓存): {code}")
    
    if all(code == first_code for code in codes_with_cache):
        print("   ✅ 缓存机制工作正常")
    else:
        print("   ❌ 缓存机制异常")
    
    print()
    
    # 3. 显示硬件组件详情
    print("3. 硬件组件详细信息...")
    components = generator.get_hardware_components(use_cache=False)
    
    print("   硬件组件:")
    for key, value in components.items():
        if key != 'timestamp':
            print(f"     {key}: {value}")
    
    print()
    
    # 4. 多次重启模拟测试
    print("4. 模拟多次重启测试...")
    restart_codes = []
    
    for i in range(3):
        # 每次都创建新的生成器实例（模拟程序重启）
        new_generator = MachineCodeGenerator()
        code = new_generator.get_machine_code(use_cache=True)
        restart_codes.append(code)
        print(f"   模拟重启 {i+1}: {code}")
    
    unique_restart = set(restart_codes)
    if len(unique_restart) == 1:
        print("   ✅ 重启后机器码稳定")
    else:
        print(f"   ❌ 重启后机器码不稳定，发现 {len(unique_restart)} 个不同的机器码")
    
    print()
    
    # 5. 总结报告
    print("=" * 80)
    print("测试总结报告")
    print("=" * 80)
    
    overall_stable = (
        len(unique_without_cache) == 1 and 
        len(unique_restart) == 1
    )
    
    if overall_stable:
        print("🎉 机器码生成稳定！")
        print(f"   最终机器码: {restart_codes[0]}")
    else:
        print("⚠️  机器码生成不稳定，需要进一步调查")
        
        if len(unique_without_cache) > 1:
            print("   问题：硬件信息获取不稳定")
            print("   建议：检查网络适配器、磁盘等硬件状态")
        
        if len(unique_restart) > 1:
            print("   问题：重启后机器码变化")
            print("   建议：检查缓存机制或硬件检测逻辑")
    
    print()
    print("缓存文件位置:", generator._cache_file)
    
    return overall_stable

def interactive_test():
    """交互式测试"""
    while True:
        print("\n" + "=" * 50)
        print("机器码稳定性测试菜单")
        print("=" * 50)
        print("1. 运行完整稳定性测试")
        print("2. 生成单个机器码")
        print("3. 清除缓存")
        print("4. 查看硬件组件信息")
        print("5. 验证机器码稳定性")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-5): ").strip()
        
        if choice == '0':
            print("退出测试工具")
            break
        elif choice == '1':
            test_machine_code_stability()
        elif choice == '2':
            generator = MachineCodeGenerator()
            code = generator.get_machine_code()
            print(f"生成的机器码: {code}")
        elif choice == '3':
            generator = MachineCodeGenerator()
            generator.clear_cache()
            print("缓存已清除")
        elif choice == '4':
            generator = MachineCodeGenerator()
            components = generator.get_hardware_components(use_cache=False)
            print("\n硬件组件信息:")
            for key, value in components.items():
                print(f"  {key}: {value}")
        elif choice == '5':
            generator = MachineCodeGenerator()
            is_stable, code = generator.validate_machine_code_stability(iterations=5)
            if is_stable:
                print(f"✅ 机器码稳定: {code}")
            else:
                print("❌ 机器码不稳定")
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        test_machine_code_stability()
