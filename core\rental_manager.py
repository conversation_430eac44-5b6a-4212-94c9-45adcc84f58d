#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
租赁车辆违章转移/分管记录查询核心模块
实现与12123网站API的交互，查询租赁车辆相关的违章分管或转移记录。
"""

import requests
import time
import datetime
# from concurrent.futures import ThreadPoolExecutor # 可选：用于并发获取分页

class RentalViolationTransferService:
    """租赁车辆违章转移/分管记录查询服务类"""

    def __init__(self, on_status_update=None, data_manager=None):
        """
        初始化查询服务。

        Args:
            on_status_update: 状态更新回调函数，用于通知UI更新状态。
                              回调函数应接受一个字符串参数 (消息)。
            data_manager: 数据管理器实例，用于记录失败查询
        """
        self.on_status_update = on_status_update
        self.data_manager = data_manager
        self.is_querying = False

        # 多Cookie管理
        self.cookies = []  # Cookie列表
        self.current_cookie_index = 0  # 当前使用的Cookie索引
        self.cookie_failure_counts = {}  # 每个Cookie的失败次数统计
        self.max_failures_per_cookie = 3  # 单个Cookie最大失败次数

        # 异常重查相关
        self.failed_wwlsh_records = []  # 失败的wwlsh记录列表

    def _update_status(self, message):
        """
        通过回调函数更新查询状态。

        Args:
            message: 状态消息。
        """
        if self.on_status_update:
            try:
                self.on_status_update(message)
            except Exception as e:
                print(f"Error in on_status_update callback: {e}")

    def set_cookies(self, cookies):
        """
        设置多个Cookie用于轮询。

        Args:
            cookies (list): Cookie字符串列表
        """
        if isinstance(cookies, str):
            cookies = [cookies]

        self.cookies = [cookie.strip() for cookie in cookies if cookie.strip()]
        self.current_cookie_index = 0
        self.cookie_failure_counts = {i: 0 for i in range(len(self.cookies))}
        self._update_status(f"已设置 {len(self.cookies)} 个Cookie用于轮询查询")

    def _get_next_available_cookie(self):
        """
        获取下一个可用的Cookie。

        Returns:
            tuple: (cookie_string, cookie_index) 或 (None, -1) 如果没有可用Cookie
        """
        if not self.cookies:
            return None, -1

        # 尝试找到失败次数未达到上限的Cookie
        for attempt in range(len(self.cookies)):
            cookie_index = (self.current_cookie_index + attempt) % len(self.cookies)
            failure_count = self.cookie_failure_counts.get(cookie_index, 0)

            if failure_count < self.max_failures_per_cookie:
                self.current_cookie_index = cookie_index
                return self.cookies[cookie_index], cookie_index

        # 如果所有Cookie都达到失败上限，重置失败计数并使用第一个
        self._update_status("所有Cookie都达到失败上限，重置失败计数")
        self.cookie_failure_counts = {i: 0 for i in range(len(self.cookies))}
        self.current_cookie_index = 0
        return self.cookies[0], 0

    def _record_cookie_failure(self, cookie_index, error_message):
        """
        记录Cookie失败。

        Args:
            cookie_index (int): Cookie索引
            error_message (str): 错误信息
        """
        if cookie_index >= 0 and cookie_index < len(self.cookies):
            self.cookie_failure_counts[cookie_index] = self.cookie_failure_counts.get(cookie_index, 0) + 1
            failure_count = self.cookie_failure_counts[cookie_index]
            self._update_status(f"Cookie #{cookie_index + 1} 失败 (第{failure_count}次): {error_message}")

            # 如果当前Cookie失败次数达到上限，切换到下一个
            if failure_count >= self.max_failures_per_cookie:
                self._update_status(f"Cookie #{cookie_index + 1} 已达到最大失败次数，将切换Cookie")
                self.current_cookie_index = (cookie_index + 1) % len(self.cookies)

    def _record_cookie_success(self, cookie_index):
        """
        记录Cookie成功，重置其失败计数。

        Args:
            cookie_index (int): Cookie索引
        """
        if cookie_index >= 0 and cookie_index < len(self.cookies):
            self.cookie_failure_counts[cookie_index] = 0

    def _fetch_page_data(self, province_code, cookie, start_date, end_date,
                         hpzl="", sfzmhm="", htbh="", page=1, size=10, retry_count=0):
        """
        获取指定页面的租赁车辆违章转移/分管记录。

        Args:
            province_code (str): 省份代码 (例如 "js")。
            cookie (str): 用户认证Cookie。
            start_date (str): 租赁合同开始日期 (格式: YYYY-MM-DD)。
            end_date (str): 租赁合同结束日期 (格式: YYYY-MM-DD)。
            hpzl (str, optional): 号牌种类代码。默认为空。
            sfzmhm (str, optional): 身份证明号码。默认为空。
            htbh (str, optional): 合同编号。默认为空。
            page (int, optional): 请求的页码。默认为1。
            size (int, optional): 每页记录数。默认为10。
            retry_count (int, optional): 重试次数。默认为0。

        Returns:
            dict: 包含API响应的字典。
                  成功时: {'success': True, 'data': {...API原始data部分...}, 'message': '查询成功', 'cookie_index': int}
                  失败时: {'success': False, 'data': None, 'message': '错误信息', 'cookie_index': int}
        """
        # 如果设置了多Cookie且传入的cookie为空，使用轮询机制（与违章查询保持一致）
        if self.cookies and not cookie:
            actual_cookie, cookie_index = self._get_next_available_cookie()
            if actual_cookie is None:
                return {"success": False, "data": None, "message": "没有可用的Cookie", "cookie_index": -1}
        else:
            actual_cookie = cookie
            cookie_index = -1

        api_url = f"https://{province_code}.122.gov.cn/vio/rent/segregate/list"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "sec-ch-ua-platform": "\"Windows\"",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Microsoft Edge\";v=\"132\"",
            "sec-ch-ua-mobile": "?0",
            "Origin": f"https://{province_code}.122.gov.cn",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": f"https://{province_code}.122.gov.cn/views/memzl/sqjl.html", # 或其他相关页面
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": actual_cookie
        }

        payload = {
            "hpzl": hpzl,
            "sfzmhm": sfzmhm,
            "htbh": htbh,
            "jdhtkssj": start_date,
            "jdhtjssj": end_date,
            "page": str(page),
            "size": str(size)
        }

        try:
            response = requests.post(api_url, headers=headers, data=payload, timeout=30)
            response.raise_for_status()  # 如果HTTP状态码是4xx或5xx，则抛出HTTPError

            response_data = response.json()
            if response_data.get("code") == 200:
                # 记录Cookie成功
                if cookie_index >= 0:
                    self._record_cookie_success(cookie_index)
                return {"success": True, "data": response_data.get("data", {}), "message": response_data.get("message", "查询成功"), "cookie_index": cookie_index}
            else:
                error_msg = response_data.get("message", "API返回未知错误")

                # 检查是否是查询频繁错误，如果是则尝试切换Cookie
                if self._is_rate_limit_error(error_msg) and cookie_index >= 0 and retry_count < len(self.cookies):
                    self._record_cookie_failure(cookie_index, error_msg)
                    self._update_status(f"检测到查询频繁错误，尝试切换Cookie重试 (第{retry_count + 1}次)")
                    time.sleep(1)  # 短暂延迟
                    return self._fetch_page_data(province_code, cookie, start_date, end_date, hpzl, sfzmhm, htbh, page, size, retry_count + 1)

                self._update_status(f"API错误 (页 {page}): {error_msg}")
                if cookie_index >= 0:
                    self._record_cookie_failure(cookie_index, error_msg)
                return {"success": False, "data": None, "message": error_msg, "cookie_index": cookie_index}

        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            self._update_status(f"请求超时 (页 {page})")
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg, "cookie_index": cookie_index}
        except requests.exceptions.ConnectionError as e:
            # 专门处理连接错误，包括连接重置
            if "10054" in str(e) or "Connection aborted" in str(e):
                error_msg = "网络连接被重置，可能是服务器限制或网络不稳定"
                self._update_status(f"连接被重置 (页 {page})，建议稍后重试")
            else:
                error_msg = f"网络连接错误: {str(e)}"
                self._update_status(f"连接错误 (页 {page}): {error_msg}")

            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg, "cookie_index": cookie_index}
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP错误: {e.response.status_code}"
            self._update_status(f"HTTP错误 (页 {page}): {e}")
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg, "cookie_index": cookie_index}
        except requests.exceptions.RequestException as e:
            error_msg = f"请求异常: {e}"
            self._update_status(f"请求异常 (页 {page}): {e}")
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg, "cookie_index": cookie_index}
        except ValueError as e: # JSONDecodeError是ValueError的子类
            error_msg = "JSON解析错误"
            self._update_status(f"JSON解析错误 (页 {page}): {e}")
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            return {"success": False, "data": None, "message": error_msg, "cookie_index": cookie_index}

    def _is_rate_limit_error(self, error_message):
        """
        判断是否是查询频繁错误。

        Args:
            error_message (str): 错误信息

        Returns:
            bool: 是否是查询频繁错误
        """
        rate_limit_keywords = [
            "查询频繁", "请求过于频繁", "访问频率过高", "请稍后再试",
            "too many requests", "rate limit", "频率限制"
        ]
        return any(keyword in error_message.lower() for keyword in rate_limit_keywords)

    def query_transfer_records(self, province_code, cookie, start_date, end_date,
                               hpzl="", sfzmhm="", htbh="",
                               initial_page=1, size_per_page=10, fetch_all_pages=True):
        """
        查询租赁车辆违章转移/分管记录。

        Args:
            province_code (str): 省份代码。
            cookie (str): 用户认证Cookie。
            start_date (str): 租赁合同开始日期 (格式: YYYY-MM-DD)。
            end_date (str): 租赁合同结束日期 (格式: YYYY-MM-DD)。
            hpzl (str, optional): 号牌种类代码。
            sfzmhm (str, optional): 身份证明号码。
            htbh (str, optional): 合同编号。
            initial_page (int, optional): 初始请求的页码。默认为1。
            size_per_page (int, optional): 每页记录数。默认为10。
            fetch_all_pages (bool, optional): 是否获取所有分页数据。默认为True。

        Returns:
            dict: 包含结果的字典:
                  {
                      'success': bool,
                      'data': list,      # 转移/分管记录列表
                      'message': str,
                      'total_count': int # 总记录数, 如果API提供
                  }
        """
        if not province_code or not cookie or not start_date or not end_date:
            return {"success": False, "data": [], "message": "省份代码、Cookie和日期范围不能为空", "total_count": 0}

        self.is_querying = True
        self._update_status(f"开始查询违章转移/分管记录 (日期: {start_date} to {end_date})...")

        all_records = []
        total_records_from_api = 0

        # 获取第一页数据以了解分页情况
        first_page_result = self._fetch_page_data(
            province_code, cookie, start_date, end_date,
            hpzl, sfzmhm, htbh, page=initial_page, size=size_per_page
        )

        if not first_page_result["success"]:
            self.is_querying = False
            self._update_status(f"查询失败: {first_page_result['message']}")
            return {"success": False, "data": [], "message": first_page_result["message"], "total_count": 0}

        api_data = first_page_result.get("data", {})
        current_page_records = api_data.get("content", [])
        all_records.extend(current_page_records)
        
        total_records_from_api = api_data.get("totalCount", 0)
        total_pages = api_data.get("totalPages", 1)
        
        self._update_status(f"获取到第 {initial_page} 页数据，共 {len(current_page_records)} 条。总计 {total_records_from_api} 条记录，{total_pages} 页。")

        if fetch_all_pages and total_pages > initial_page:
            self._update_status(f"需要获取剩余 {total_pages - initial_page} 页数据...")
            # 可选：此处可以使用ThreadPoolExecutor并发获取
            for page_num in range(initial_page + 1, total_pages + 1):
                if not self.is_querying:
                    self._update_status("查询已中止。")
                    break
                
                self._update_status(f"正在获取第 {page_num} 页...")
                time.sleep(0.1) # 轻微延迟避免请求过于频繁 (可选)
                page_result = self._fetch_page_data(
                    province_code, cookie, start_date, end_date,
                    hpzl, sfzmhm, htbh, page=page_num, size=size_per_page
                )
                if page_result["success"]:
                    page_records = page_result.get("data", {}).get("content", [])
                    all_records.extend(page_records)
                    self._update_status(f"已获取第 {page_num} 页数据，{len(page_records)} 条。当前总数: {len(all_records)}")
                else:
                    # 即使某页失败，也继续尝试获取其他页，但记录错误
                    self._update_status(f"获取第 {page_num} 页失败: {page_result['message']}")
                    # 可以选择在这里中止整个查询，或者标记部分数据可能丢失
                    # break # 如果一页失败就中止

        self.is_querying = False
        final_message = f"查询完成。共获取 {len(all_records)} 条记录。"
        if total_records_from_api > 0 and len(all_records) != total_records_from_api and fetch_all_pages:
             final_message += f" (API报告总数为 {total_records_from_api}，可能部分页面获取失败或数据不一致)"
        
        self._update_status(final_message)
        return {
            "success": True, 
            "data": all_records, 
            "message": final_message,
            "total_count": total_records_from_api if total_records_from_api > 0 else len(all_records)
        }

    def stop_query(self):
        """停止当前的查询过程。"""
        if self.is_querying:
            self.is_querying = False
            self._update_status("查询操作已由用户请求停止。")
        else:
            self._update_status("当前没有正在进行的查询操作。")

    def query_transfer_record_details(self, province_code, cookie, wwlsh, page=1, size=10, retry_count=0):
        """
        查询特定违章转移记录的详细信息。

        Args:
            province_code (str): 省份代码。
            cookie (str): 用户认证Cookie。
            wwlsh (str): 网办流水号，从transfer_records查询结果中获取。
            page (int, optional): 页码，默认为1。
            size (int, optional): 每页记录数，默认为10。
            retry_count (int, optional): 重试次数，默认为0。

        Returns:
            dict: 包含查询结果的字典:
                 {
                     'success': bool,
                     'data': list,      # 违章详细记录列表
                     'message': str,
                     'cookie_index': int,  # 使用的Cookie索引
                     'wwlsh': str,     # 网办流水号，用于异常记录
                 }
        """
        # 检查必需参数，但如果设置了多Cookie，允许空cookie参数
        if not province_code or not wwlsh:
            return {"success": False, "data": [], "message": "省份代码和网办流水号不能为空", "wwlsh": wwlsh}

        # 如果没有设置多Cookie且cookie为空，则报错
        if not self.cookies and not cookie:
            return {"success": False, "data": [], "message": "Cookie不能为空（未设置多Cookie时）", "wwlsh": wwlsh}

        self._update_status(f"开始查询违章转移记录详情 (wwlsh: {wwlsh})...")

        # 添加延迟避免请求过于频繁
        time.sleep(1.5)

        # 如果设置了多Cookie且传入的cookie为空，使用轮询机制（与违章查询保持一致）
        if self.cookies and not cookie:
            actual_cookie, cookie_index = self._get_next_available_cookie()
            if actual_cookie is None:
                error_msg = "没有可用的Cookie"
                self._record_failed_wwlsh(wwlsh, error_msg, province_code)
                return {"success": False, "data": [], "message": error_msg, "cookie_index": -1, "wwlsh": wwlsh}
        else:
            # 使用传入的cookie（可能是具体的cookie或空字符串）
            if not cookie:
                error_msg = "Cookie不能为空（未设置多Cookie或传入了具体Cookie时）"
                self._record_failed_wwlsh(wwlsh, error_msg, province_code)
                return {"success": False, "data": [], "message": error_msg, "cookie_index": -1, "wwlsh": wwlsh}
            actual_cookie = cookie
            cookie_index = -1

        try:
            api_url = f"https://{province_code}.122.gov.cn/vio/rent/segregate/surlist"

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Connection": "keep-alive",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "sec-ch-ua-platform": "\"Windows\"",
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Microsoft Edge\";v=\"132\"",
                "sec-ch-ua-mobile": "?0",
                "Origin": f"https://{province_code}.122.gov.cn",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": f"https://{province_code}.122.gov.cn/views/memzl/sqjlDetail.html",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Cookie": actual_cookie
            }

            payload = {
                "wwlsh": wwlsh,
                "page": str(page),
                "size": str(size)
            }

            response = requests.post(api_url, headers=headers, data=payload, timeout=30)
            response.raise_for_status()  # 如果HTTP状态码是4xx或5xx，则抛出HTTPError

            response_data = response.json()
            if response_data.get("code") == 200:
                violation_details = response_data.get("data", [])
                self._update_status(f"成功获取违章详情，共 {len(violation_details)} 条记录。")
                # 记录Cookie成功
                if cookie_index >= 0:
                    self._record_cookie_success(cookie_index)
                return {
                    "success": True,
                    "data": violation_details,
                    "message": response_data.get("message", "查询成功"),
                    "cookie_index": cookie_index,
                    "wwlsh": wwlsh
                }
            else:
                error_msg = response_data.get("message", "API返回未知错误")

                # 检查是否是查询频繁错误，如果是则尝试切换Cookie
                if self._is_rate_limit_error(error_msg) and cookie_index >= 0 and retry_count < len(self.cookies):
                    self._record_cookie_failure(cookie_index, error_msg)
                    self._update_status(f"检测到查询频繁错误，尝试切换Cookie重试 (第{retry_count + 1}次)")
                    time.sleep(1)  # 短暂延迟
                    return self.query_transfer_record_details(province_code, cookie, wwlsh, page, size, retry_count + 1)

                self._update_status(f"查询违章详情失败: {error_msg}")
                if cookie_index >= 0:
                    self._record_cookie_failure(cookie_index, error_msg)
                self._record_failed_wwlsh(wwlsh, error_msg, province_code)
                return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            self._update_status(error_msg)
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            self._record_failed_wwlsh(wwlsh, error_msg, province_code)
            return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}
        except requests.exceptions.ConnectionError as e:
            # 专门处理连接错误，包括连接重置
            if "10054" in str(e) or "Connection aborted" in str(e):
                error_msg = "网络连接被重置，可能是服务器限制或网络不稳定"
                self._update_status(f"连接被重置 (wwlsh: {wwlsh})，建议稍后重试")
            else:
                error_msg = f"网络连接错误: {str(e)}"
                self._update_status(f"连接错误 (wwlsh: {wwlsh}): {error_msg}")

            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            self._record_failed_wwlsh(wwlsh, error_msg, province_code)
            return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP错误: {e.response.status_code}"
            self._update_status(error_msg)
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            self._record_failed_wwlsh(wwlsh, error_msg, province_code)
            return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}
        except requests.exceptions.RequestException as e:
            error_msg = f"请求异常: {e}"
            self._update_status(error_msg)
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            self._record_failed_wwlsh(wwlsh, error_msg, province_code)
            return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}
        except ValueError as e:  # JSONDecodeError是ValueError的子类
            error_msg = f"JSON解析错误: {e}"
            self._update_status(error_msg)
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            self._record_failed_wwlsh(wwlsh, error_msg, province_code)
            return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}
        except Exception as e:
            error_msg = f"发生未预期的错误: {e}"
            self._update_status(error_msg)
            if cookie_index >= 0:
                self._record_cookie_failure(cookie_index, error_msg)
            self._record_failed_wwlsh(wwlsh, error_msg, province_code)
            return {"success": False, "data": [], "message": error_msg, "cookie_index": cookie_index, "wwlsh": wwlsh}

    def _record_failed_wwlsh(self, wwlsh, error_message, province_code):
        """
        记录失败的wwlsh查询。

        Args:
            wwlsh (str): 网办流水号
            error_message (str): 错误信息
            province_code (str): 省份代码
        """
        failed_record = {
            'wwlsh': wwlsh,
            'error_message': error_message,
            'province_code': province_code,
            'failed_time': time.strftime("%Y-%m-%d %H:%M:%S"),
            'retry_count': 0
        }

        # 检查是否已存在相同wwlsh的失败记录
        existing_index = None
        for i, record in enumerate(self.failed_wwlsh_records):
            if record['wwlsh'] == wwlsh:
                existing_index = i
                break

        if existing_index is not None:
            # 更新现有记录
            self.failed_wwlsh_records[existing_index] = failed_record
        else:
            # 添加新记录
            self.failed_wwlsh_records.append(failed_record)

        # 如果有数据管理器，也记录到数据管理器中
        if self.data_manager:
            self.data_manager.add_failed_query(
                wwlsh,
                error_message,
                {'province_code': province_code, 'query_type': 'transfer_detail'}
            )

    def get_failed_wwlsh_records(self):
        """
        获取所有失败的wwlsh记录。

        Returns:
            list: 失败记录列表
        """
        return self.failed_wwlsh_records.copy()

    def remove_failed_wwlsh(self, wwlsh):
        """
        移除失败的wwlsh记录。

        Args:
            wwlsh (str): 网办流水号
        """
        self.failed_wwlsh_records = [record for record in self.failed_wwlsh_records if record['wwlsh'] != wwlsh]

        # 如果有数据管理器，也从数据管理器中移除
        if self.data_manager:
            self.data_manager.remove_failed_query(wwlsh)

    def clear_failed_wwlsh_records(self):
        """
        清空所有失败的wwlsh记录。
        """
        self.failed_wwlsh_records = []

if __name__ == '__main__':
    # 模块使用示例 (需要替换为有效的Cookie和参数)
    def sample_status_update(message):
        print(f"[状态更新] {datetime.datetime.now()}: {message}")

    service = RentalViolationTransferService(on_status_update=sample_status_update)
    
    # 替换为真实的参数
    # province = "js" 
    # user_cookie = "你的真实cookie字符串"
    # s_date = "2025-01-01"
    # e_date = "2025-05-10"
    
    # print(f"正在使用示例参数调用服务 (请确保替换为真实有效的Cookie和参数)...")
    # result = service.query_transfer_records(
    #     province_code=province,
    #     cookie=user_cookie,
    #     start_date=s_date,
    #     end_date=e_date,
    #     # hpzl="52", # 可选示例：仅查询小型新能源汽车
    #     fetch_all_pages=True # 获取所有分页
    # )

    # print("\n查询结果:")
    # if result["success"]:
    #     print(f"成功获取 {len(result['data'])} 条记录 (API报告总数: {result['total_count']})。")
    #     # for i, record in enumerate(result["data"][:3]): # 打印前3条记录示例
    #     #     print(f"  记录 {i+1}: WWLSH={record.get('wwlsh')}, HPHM={record.get('hphm')}, HTBH={record.get('htbh')}, CJSJ={record.get('cjsj')}, ZT={record.get('zt')}")
    #     
    #     # 获取第一条记录的详情
    #     # if result["data"]:
    #     #     first_record = result["data"][0]
    #     #     wwlsh = first_record.get("wwlsh")
    #     #     print(f"\n正在查询第一条记录的详情 (wwlsh: {wwlsh})...")
    #     #     details_result = service.query_transfer_record_details(
    #     #         province_code=province,
    #     #         cookie=user_cookie,
    #     #         wwlsh=wwlsh
    #     #     )
    #     #     if details_result["success"]:
    #     #         print(f"获取到 {len(details_result['data'])} 条违章详情记录")
    #     #         for i, detail in enumerate(details_result["data"]):
    #     #             print(f"\n违章 {i+1}:")
    #     #             print(f"  车牌号: {detail.get('hphm')}")
    #     #             print(f"  违法行为: {detail.get('wfms')}")
    #     #             print(f"  违法地点: {detail.get('wfdz')}")
    #     #             print(f"  违法时间: {detail.get('wfsj')}")
    #     #             print(f"  号牌类型: {detail.get('hpzlStr')}")
    #     #     else:
    #     #         print(f"查询详情失败: {details_result['message']}")
    # else:
    #     print(f"查询失败: {result['message']}")

    # 也可以直接查询特定网办流水号的违章详情
    # wwlsh = "632002505080013105"  # 替换为实际的网办流水号
    # details_result = service.query_transfer_record_details(
    #     province_code=province,
    #     cookie=user_cookie,
    #     wwlsh=wwlsh
    # )
    # if details_result["success"]:
    #     print(f"\n直接查询违章详情成功，共 {len(details_result['data'])} 条记录")
    #     for i, detail in enumerate(details_result["data"]):
    #         print(f"\n违章详情 {i+1}:")
    #         print(f"  车牌: {detail.get('hphm')}")
    #         print(f"  违法描述: {detail.get('wfms')}")
    #         print(f"  违法地点: {detail.get('wfdz')}")
    #         print(f"  违法时间: {detail.get('wfsj')}")
    #         print(f"  号牌类型: {detail.get('hpzlStr')}")
    # else:
    #     print(f"\n查询违章详情失败: {details_result['message']}")

    print("\n模块 RentalViolationTransferService 已定义。取消注释并提供有效参数以进行测试。") 