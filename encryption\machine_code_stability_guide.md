# 机器码稳定性解决方案

## 问题描述

在某些机器上，每次打开软件后生成的机器码会发生变化，导致授权失效。这通常是由于以下原因造成的：

1. **网络适配器变化**：虚拟网卡、VPN连接等导致MAC地址不稳定
2. **硬件信息获取不一致**：WMI查询结果在不同时间可能返回不同的顺序
3. **系统状态变化**：磁盘分区、网络配置等系统状态的变化

## 解决方案

### 1. 改进的机器码生成算法

新的机器码生成器包含以下改进：

#### 稳定的MAC地址选择
```python
def get_stable_mac_address(self):
    """获取最稳定的MAC地址"""
    # 优先级排序：物理网卡 > 启用IP > 以太网 > MAC地址字典序
    # 避免选择虚拟网卡和临时网卡
```

#### 系统盘优先的磁盘序列号
```python
def get_disk_serial(self):
    """获取硬盘序列号 - 选择最稳定的系统盘"""
    # 优先获取C盘对应的物理磁盘序列号
    # 确保每次都选择相同的磁盘
```

#### 增加系统UUID
```python
def get_system_uuid(self):
    """获取系统UUID作为额外的稳定标识"""
    # 使用系统UUID增强机器码的唯一性和稳定性
```

### 2. 缓存机制

#### 硬件信息缓存
- 首次获取硬件信息后，保存到本地缓存文件
- 后续启动时优先使用缓存的硬件信息
- 缓存文件位置：`%TEMP%/YiChengTech/machine_components.cache`

#### 缓存策略
```python
def get_machine_code(self, use_cache=True):
    """生成稳定的机器码"""
    # use_cache=True: 使用缓存提高稳定性
    # use_cache=False: 强制重新获取硬件信息
```

### 3. 配置选项

在 `config.py` 中添加了新的配置选项：

```python
AUTH_CONFIG = {
    # ... 其他配置 ...
    "use_machine_code_cache": True,     # 是否使用机器码缓存
    "machine_code_validation": True     # 是否在启动时验证稳定性
}
```

## 使用方法

### 1. 测试机器码稳定性

运行稳定性测试工具：

```bash
# 完整测试
python encryption/test_machine_code_stability.py

# 交互式测试
python encryption/test_machine_code_stability.py --interactive
```

### 2. 在代码中使用

```python
from encryption.auth_manager import AuthManager

# 创建授权管理器（自动使用稳定的机器码）
auth_manager = AuthManager()

# 获取机器码
machine_code = auth_manager.get_machine_code()

# 验证机器码稳定性
is_stable, stable_code = auth_manager.validate_machine_code_stability()

# 刷新机器码（清除缓存）
changed = auth_manager.refresh_machine_code()
```

### 3. 故障排除

#### 如果机器码仍然不稳定：

1. **清除缓存重新生成**：
   ```python
   auth_manager.refresh_machine_code()
   ```

2. **禁用缓存机制**：
   ```python
   # 在config.py中设置
   "use_machine_code_cache": False
   ```

3. **查看详细的硬件信息**：
   ```python
   generator = MachineCodeGenerator()
   components = generator.get_hardware_components(use_cache=False)
   print(components)
   ```

#### 常见问题解决：

**问题1：网络适配器频繁变化**
- 解决：新算法会优先选择物理网卡，避免虚拟网卡
- 如果仍有问题，可以手动指定稳定的MAC地址

**问题2：磁盘序列号获取失败**
- 解决：新算法会优先获取系统盘序列号
- 备用方案：使用系统UUID作为补充标识

**问题3：主板序列号无效**
- 解决：过滤掉常见的无效序列号（如"Default string"）
- 备用方案：使用其他硬件特征组合

## 技术细节

### 缓存文件格式

```json
{
  "cpu_serial": "BFEBFBFF000906E9",
  "disk_serial": "WD-WCC4N7XXXXXX",
  "mac_address": "1C1B0D123456",
  "board_serial": "M1234567890123",
  "system_uuid": "12345678901234567890123456789012",
  "timestamp": "1640995200"
}
```

### 机器码生成公式

```
machine_code = SHA256(
    prefix + 
    mac_address + 
    cpu_serial + 
    disk_serial + 
    board_serial + 
    system_uuid + 
    suffix
).upper()
```

### 安全性改进

1. **使用SHA-256替代MD5**：提高安全性，避免碰撞攻击
2. **多重硬件特征**：增加系统UUID，提高唯一性
3. **缓存保护**：缓存文件包含时间戳，防止恶意修改

## 部署建议

### 1. 渐进式部署

1. 首先在测试环境验证稳定性
2. 对现有用户，保持向后兼容
3. 新用户使用新的机器码算法

### 2. 监控和日志

- 启用详细日志记录机器码生成过程
- 监控机器码变化频率
- 收集用户反馈，持续优化

### 3. 用户支持

- 提供机器码重置工具
- 建立机器码变化的处理流程
- 准备常见问题解答文档

## 总结

通过以上改进，机器码的稳定性将大大提高：

✅ **稳定的硬件选择算法**：优先选择最稳定的硬件特征  
✅ **智能缓存机制**：避免重复获取硬件信息  
✅ **多重备用方案**：确保在各种环境下都能工作  
✅ **详细的调试工具**：便于问题诊断和解决  
✅ **向后兼容性**：不影响现有用户的使用  

这个解决方案应该能够解决大部分机器码不稳定的问题。如果在特定环境下仍有问题，可以通过调试工具进一步分析和优化。
