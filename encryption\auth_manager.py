import os
import json
import sys # Import sys
from datetime import datetime
import traceback # Added for detailed error logging
from encryption.config import AUTH_CONFIG
from encryption.aes_crypto import AES<PERSON>rypto
from encryption.machine_code import Machine<PERSON>ode<PERSON>enerator
from encryption.cloud_storage import CloudStorage
from utils.constants import PROVINCE_LETTER_CODE_TO_SHORT_NAME

# --- Debug Logging Setup ---
def log_to_desktop(message):
    try:
        desktop = os.path.join(os.path.join(os.environ['USERPROFILE']), 'Desktop')
        log_file_path = os.path.join(desktop, "auth_debug_log.txt")
        # Ensure message is a string
        if not isinstance(message, str):
            message = str(message)
        with open(log_file_path, "a", encoding="utf-8") as f:
            f.write(f"{datetime.now()}: {message}\n")
    except Exception as log_e:
        # If logging to desktop fails, print to stderr as a last resort (visible if run from cmd)
        print(f"Logging to desktop failed: {log_e}", file=sys.stderr)
        print(f"Original log message: {message}", file=sys.stderr)

log_to_desktop("--- AuthManager Module Loaded ---")
# --- End Debug Logging Setup ---

# Helper function to get the appropriate app data directory
def get_app_data_path(file_name):
    app_name = "ViolationQueryApp" # Should ideally match APP_NAME in build.py
    log_to_desktop(f"get_app_data_path: called for {file_name}")
    if hasattr(sys, '_MEIPASS'):
        # For packaged app, use user's AppData directory
        base_path = os.path.join(os.getenv('APPDATA'), app_name, "encryption")
        log_to_desktop(f"get_app_data_path: MEIPASS detected, base_path = {base_path}")
    else:
        # For development, use the local encryption folder relative to this file's parent
        base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "encryption_data_dev"))
        log_to_desktop(f"get_app_data_path: Dev environment, base_path = {base_path}")

    if not os.path.exists(base_path):
        log_to_desktop(f"get_app_data_path: Directory {base_path} does not exist, attempting to create.")
        try:
            os.makedirs(base_path, exist_ok=True)
            log_to_desktop(f"get_app_data_path: Directory {base_path} created or already exists.")
        except Exception as e:
            log_to_desktop(f"get_app_data_path: CRITICAL - Error creating directory {base_path}: {e}\n{traceback.format_exc()}")
            # Fallback or raise error
            # For simplicity, we'll let it fail if creation fails, file ops will then fail
    final_path = os.path.join(base_path, file_name)
    log_to_desktop(f"get_app_data_path: final path for {file_name} is {final_path}")
    return final_path

# Helper function to get path to packaged resource (read-only)
def get_packaged_resource_path(relative_path_in_bundle):
    log_to_desktop(f"get_packaged_resource_path: called for {relative_path_in_bundle}")
    if hasattr(sys, '_MEIPASS'):
        # Path within the PyInstaller bundle
        path = os.path.join(sys._MEIPASS, relative_path_in_bundle)
        log_to_desktop(f"get_packaged_resource_path: MEIPASS detected, path = {path}")
        return path
    else:
        # Path for development (relative to project root, assuming auth_manager.py is in encryption/)
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        path = os.path.join(project_root, relative_path_in_bundle)
        log_to_desktop(f"get_packaged_resource_path: Dev environment, path = {path}")
        return path

class AuthManager:
    def __init__(self):
        log_to_desktop("AuthManager: __init__ started.")
        try:
            self._user_local_auth_file = get_app_data_path(AUTH_CONFIG["local_auth_file"]) # e.g., auth.dat
            log_to_desktop(f"AuthManager: _user_local_auth_file = {self._user_local_auth_file}")
            self._user_query_count_file = get_app_data_path("query_count.json")
            log_to_desktop(f"AuthManager: _user_query_count_file = {self._user_query_count_file}")

            # Packaged (read-only) paths - used as fallback or initial template
            self._packaged_local_auth_file = get_packaged_resource_path(os.path.join("encryption", AUTH_CONFIG["local_auth_file"]))
            log_to_desktop(f"AuthManager: _packaged_local_auth_file = {self._packaged_local_auth_file}")
            self._packaged_query_count_file = get_packaged_resource_path(os.path.join("encryption", "query_count.json"))
            log_to_desktop(f"AuthManager: _packaged_query_count_file = {self._packaged_query_count_file}")
            
            self._aes_key = AUTH_CONFIG["aes_key"].encode("utf-8")
            self._aes_iv = AUTH_CONFIG["aes_iv"].encode("utf-8")
            self._crypto = AESCrypto(self._aes_key, self._aes_iv)
            log_to_desktop("AuthManager: AESCrypto initialized.")
            self._machine_code_generator = MachineCodeGenerator() # Store instance

            # 获取机器码，使用配置中的缓存设置
            use_cache = AUTH_CONFIG.get("use_machine_code_cache", True)
            self._machine_code = self._machine_code_generator.get_machine_code(use_cache=use_cache)
            log_to_desktop(f"AuthManager: Machine code = {self._machine_code}")

            # 可选的机器码稳定性验证
            if AUTH_CONFIG.get("machine_code_validation", False):
                log_to_desktop("AuthManager: Validating machine code stability...")
                is_stable, _ = self._machine_code_generator.validate_machine_code_stability(iterations=3)
                if not is_stable:
                    log_to_desktop("AuthManager: WARNING - Machine code is not stable!")
                else:
                    log_to_desktop("AuthManager: Machine code stability validated.")
            self._auth_data = None
            # Load query count from user-writable location
            self._query_count = self._load_query_count()
            log_to_desktop(f"AuthManager: Initial query count loaded: {self._query_count}")
            log_to_desktop("AuthManager: __init__ completed successfully.")
        except Exception as e_init:
            log_to_desktop(f"AuthManager: CRITICAL ERROR in __init__: {e_init}\n{traceback.format_exc()}")
            raise

    def delete_local_auth_file(self):
        log_to_desktop("AuthManager: delete_local_auth_file called.")
        try:
            if os.path.exists(self._user_local_auth_file):
                os.remove(self._user_local_auth_file)
                log_to_desktop(f"AuthManager: Successfully deleted local auth file: {self._user_local_auth_file}")
                self._auth_data = None # Clear cached auth data as well
                return True
            else:
                log_to_desktop(f"AuthManager: Local auth file not found, no need to delete: {self._user_local_auth_file}")
                return True # No error if not found
        except Exception as e:
            log_to_desktop(f"AuthManager: ERROR deleting local auth file {self._user_local_auth_file}: {e}\n{traceback.format_exc()}")
            return False

    def get_machine_code(self):
        log_to_desktop(f"AuthManager: get_machine_code called, returning {self._machine_code}")
        return self._machine_code

    def refresh_machine_code(self):
        """刷新机器码（清除缓存并重新生成）"""
        log_to_desktop("AuthManager: refresh_machine_code called")
        try:
            self._machine_code_generator.clear_cache()
            use_cache = AUTH_CONFIG.get("use_machine_code_cache", True)
            new_machine_code = self._machine_code_generator.get_machine_code(use_cache=use_cache)

            if new_machine_code != self._machine_code:
                log_to_desktop(f"AuthManager: Machine code changed from {self._machine_code} to {new_machine_code}")
                self._machine_code = new_machine_code
                # 机器码变化时，清除本地授权数据
                self.delete_local_auth_file()
                self._auth_data = None
                return True
            else:
                log_to_desktop("AuthManager: Machine code unchanged after refresh")
                return False
        except Exception as e:
            log_to_desktop(f"AuthManager: Error refreshing machine code: {e}\n{traceback.format_exc()}")
            return False

    def validate_machine_code_stability(self):
        """验证机器码稳定性"""
        log_to_desktop("AuthManager: validate_machine_code_stability called")
        try:
            is_stable, stable_code = self._machine_code_generator.validate_machine_code_stability(iterations=3)
            log_to_desktop(f"AuthManager: Machine code stability check - stable: {is_stable}")

            if not is_stable:
                log_to_desktop("AuthManager: Machine code is unstable, this may cause authorization issues")

            return is_stable, stable_code
        except Exception as e:
            log_to_desktop(f"AuthManager: Error validating machine code stability: {e}\n{traceback.format_exc()}")
            return False, None

    def is_authorized(self):
        log_to_desktop("AuthManager: is_authorized called.")
        data = self.get_auth_data()
        if not data:
            return False
        # 优先expire_date
        expire = data.get("expire_date")
        if expire:
            try:
                expire_dt = datetime.strptime(expire, "%Y-%m-%d")
                return expire_dt >= datetime.now()
            except Exception:
                return False
        # 兼容expire_time
        expire_time = data.get("expire_time")
        if expire_time is not None:
            if expire_time == -1:
                return True
            try:
                expire_dt = datetime.fromtimestamp(int(expire_time))
                return expire_dt >= datetime.now()
            except Exception:
                return False
        return False

    def get_expire_date_str(self):
        data = self.get_auth_data()
        if not data:
            return "未授权"
        expire = data.get("expire_date")
        if expire:
            return expire
        expire_time = data.get("expire_time")
        if expire_time is not None:
            if expire_time == -1:
                return "永久"
            try:
                expire_dt = datetime.fromtimestamp(int(expire_time))
                return expire_dt.strftime("%Y-%m-%d")
            except Exception:
                return "无效"
        return "未授权"

    def get_auth_data(self, force_refresh=False):
        log_to_desktop(f"AuthManager: get_auth_data called with force_refresh={force_refresh}. NOTE: Local file check is disabled.")
        
        # Always try to fetch from cloud if _auth_data is None or force_refresh is True.
        # Local file loading is removed.
        if self._auth_data and not force_refresh:
            log_to_desktop("AuthManager: Returning cached _auth_data.")
            return self._auth_data
        
        log_to_desktop("AuthManager: Attempting to fetch auth_data from cloud.")
        self._auth_data = None # Reset before cloud fetch
        try:
            cloud = CloudStorage()
            content = cloud.get_content()
            log_to_desktop(f"AuthManager: Cloud content received: {content[:200] if content else 'None'}...") # Log first 200 chars
            if not content:
                log_to_desktop("AuthManager: Cloud content is empty.")
                return None

            for line_num, line in enumerate(content.splitlines()):
                line = line.strip()
                log_to_desktop(f"AuthManager: Processing cloud line {line_num + 1}: '{line}'")
                if not line or line.startswith("//"):
                    continue
                if ";" in line:
                    parts = line.split(";", 1)
                    if len(parts) == 2:
                        machine_code_from_cloud, encrypted_data = parts[0].strip(), parts[1].strip()
                        log_to_desktop(f"AuthManager: Parsed from cloud - MC: {machine_code_from_cloud}, ED: {encrypted_data}")
                        if machine_code_from_cloud == self._machine_code:
                            log_to_desktop("AuthManager: Cloud machine code MATCHES local machine code.")
                            try:
                                dec = self._crypto.decrypt_from_base64(encrypted_data)
                                log_to_desktop(f"AuthManager: Decrypted cloud data: {dec}")
                                data = json.loads(dec)
                                if data.get("machine_code") == self._machine_code:
                                    log_to_desktop("AuthManager: Machine code in decrypted data MATCHES. Auth successful from cloud.")
                                    self._auth_data = data
                                    # DO NOT SAVE LOCALLY: self._save_local_auth(encrypted_data) 
                                    return data
                                else:
                                    log_to_desktop("AuthManager: Machine code in decrypted data MISMATCH.")
                            except Exception as e_cloud_decrypt:
                                log_to_desktop(f"AuthManager: ERROR decrypting/loading cloud data for matched MC: {e_cloud_decrypt}\n{traceback.format_exc()}")
                                continue # Try next line from cloud
                        else:
                            log_to_desktop("AuthManager: Cloud machine code MISMATCH local machine code.")
                    # If parts len is not 2, it's a malformed line for this format, skip.
                    continue # Try next line
                
                # Fallback for old format (no semicolon) - This part might be deprecated if all cloud data is new format
                try: 
                    log_to_desktop("AuthManager: Trying fallback (no semicolon) cloud data format.")
                    dec = self._crypto.decrypt_from_base64(line)
                    log_to_desktop(f"AuthManager: Decrypted fallback cloud data: {dec}")
                    data = json.loads(dec)
                    if data.get("machine_code") == self._machine_code:
                        log_to_desktop("AuthManager: Fallback cloud data - Machine code MATCHES. Auth successful.")
                        self._auth_data = data
                        # DO NOT SAVE LOCALLY: self._save_local_auth(line)
                        return data
                    else:
                        log_to_desktop("AuthManager: Fallback cloud data - Machine code MISMATCH.")
                except Exception as e_cloud_fallback_decrypt:
                    log_to_desktop(f"AuthManager: ERROR decrypting/loading fallback cloud data: {e_cloud_fallback_decrypt}\n{traceback.format_exc()}")
                    # Malformed line for this format, or decryption error, try next line
                    continue 
            
            log_to_desktop("AuthManager: Finished processing cloud content. No matching auth data found in cloud for this machine.")
            return None # Explicitly return None if no auth data found in cloud after checking all lines
            
        except Exception as e_cloud_general:
            log_to_desktop(f"AuthManager: GENERAL ERROR during cloud auth: {e_cloud_general}\n{traceback.format_exc()}")
            # If cloud fetch fails, it means no authorization.
            return None

        # All local file loading logic is removed.
        # If cloud fails or no matching data, it's unauthorized.
        # log_to_desktop(f"AuthManager: No authorization found from cloud. Returning None.")
        # return None # Should be covered by return None inside the try/except for cloud processing

    def _save_local_auth(self, line):
        log_to_desktop(f"AuthManager: _save_local_auth called to save line: '{line}' to {self._user_local_auth_file}")
        # Always save to user-writable path
        auth_file_to_save = self._user_local_auth_file
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(auth_file_to_save), exist_ok=True)
            log_to_desktop(f"AuthManager: Ensured directory {os.path.dirname(auth_file_to_save)} exists for saving.")
            with open(auth_file_to_save, "w", encoding="utf-8") as f:
                f.write(line)
            log_to_desktop(f"AuthManager: Successfully wrote to {auth_file_to_save}")
        except Exception as e:
            log_to_desktop(f"AuthManager: CRITICAL - Error saving local auth to {auth_file_to_save}: {e}\n{traceback.format_exc()}")

    def get_allowed_pages(self):
        data = self.get_auth_data()
        return data.get("allowed_pages", []) if data else []

    def get_allowed_provinces(self):
        data = self.get_auth_data()
        return data.get("allowed_provinces", []) if data else []

    def get_allowed_prefixes(self):
        data = self.get_auth_data()
        return data.get("allowed_prefixes", []) if data else []

    def get_monthly_query_limit(self):
        data = self.get_auth_data()
        return int(data.get("monthly_query_limit", 0)) if data else 0

    def _load_query_count(self):
        log_to_desktop(f"AuthManager: _load_query_count called. User file: {self._user_query_count_file}, Packaged file: {self._packaged_query_count_file}")
        # Load from user-writable path
        count_file_to_load = self._user_query_count_file
        if os.path.exists(count_file_to_load):
            log_to_desktop(f"AuthManager: User query count file {count_file_to_load} exists. Trying to read.")
            try:
                with open(count_file_to_load, "r", encoding="utf-8") as f:
                    counts = json.load(f)
                log_to_desktop(f"AuthManager: Loaded query counts from user file: {counts}")
                return counts
            except Exception as e:
                log_to_desktop(f"AuthManager: Error loading query count from {count_file_to_load}: {e}\n{traceback.format_exc()}")
                # If corrupted, perhaps delete and return empty, or try packaged one as template
                if os.path.exists(self._packaged_query_count_file):
                    log_to_desktop(f"AuthManager: Trying to use packaged query count file {self._packaged_query_count_file} as template.")
                    try:
                        with open(self._packaged_query_count_file, "r", encoding="utf-8") as f_pkg:
                            # Copy packaged template to user location if user one is bad
                            initial_counts = json.load(f_pkg)
                            self._query_count = initial_counts # Set internal state
                            self._save_query_count() # Save this template to user dir
                            log_to_desktop(f"AuthManager: Copied packaged query counts to user file. Counts: {initial_counts}")
                            return initial_counts
                    except Exception as e_pkg_qc:
                        log_to_desktop(f"AuthManager: Error loading/copying packaged query count: {e_pkg_qc}\n{traceback.format_exc()}")
                return {} # Default to empty if all fails
        
        # If user file doesn't exist, try to load (and copy) from packaged file if it exists
        if os.path.exists(self._packaged_query_count_file):
            log_to_desktop(f"AuthManager: User query count file does not exist. Trying packaged file {self._packaged_query_count_file} as initial.")
            try:
                with open(self._packaged_query_count_file, "r", encoding="utf-8") as f_pkg:
                    initial_counts = json.load(f_pkg)
                    self._query_count = initial_counts # Set internal state before saving
                    self._save_query_count() # This will create the user file
                    log_to_desktop(f"AuthManager: Loaded initial query counts from packaged file and saved to user file. Counts: {initial_counts}")
                    return initial_counts
            except Exception as e_pkg_qc_init:
                log_to_desktop(f"AuthManager: Error loading initial query count from packaged file: {e_pkg_qc_init}\n{traceback.format_exc()}")
        
        log_to_desktop("AuthManager: _load_query_count returning empty dict as no query count file found/loaded.")
        return {} # Default to empty if no file found

    def _save_query_count(self):
        log_to_desktop(f"AuthManager: _save_query_count called to save data: {self._query_count} to {self._user_query_count_file}")
        # Always save to user-writable path
        count_file_to_save = self._user_query_count_file
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(count_file_to_save), exist_ok=True)
            log_to_desktop(f"AuthManager: Ensured directory {os.path.dirname(count_file_to_save)} exists for saving query count.")
            with open(count_file_to_save, "w", encoding="utf-8") as f:
                json.dump(self._query_count, f, ensure_ascii=False, indent=4) # Added indent for readability
            log_to_desktop(f"AuthManager: Successfully wrote query count to {count_file_to_save}")
        except Exception as e:
            log_to_desktop(f"AuthManager: CRITICAL - Error saving query count to {count_file_to_save}: {e}\n{traceback.format_exc()}")

    def get_current_month_query_count(self):
        now = datetime.now().strftime("%Y-%m")
        return int(self._query_count.get(now, 0))

    def get_current_day_query_count(self):
        """获取当天查询次数"""
        now = datetime.now().strftime("%Y-%m-%d")
        return int(self._query_count.get(now, 0))

    def get_daily_query_limit(self):
        """获取每日查询限制，从授权数据中获取，默认为10次"""
        data = self.get_auth_data()
        if not data:
            return 10

        daily_limit = data.get("daily_query_limit", 10)
        try:
            daily_limit = int(daily_limit)
            # 如果限制值小于等于0，使用默认值10
            return daily_limit if daily_limit > 0 else 10
        except (ValueError, TypeError):
            return 10

    def can_query_today(self):
        """检查今天是否还能查询"""
        daily_limit = self.get_daily_query_limit()
        current_daily_count = self.get_current_day_query_count()
        return current_daily_count < daily_limit

    def can_query(self):
        """检查是否可以查询（同时检查月度和每日限制）"""
        # 检查月度限制
        monthly_limit = self.get_monthly_query_limit()
        if monthly_limit > 0:
            if self.get_current_month_query_count() >= monthly_limit:
                return False

        # 检查每日限制
        if not self.can_query_today():
            return False

        return True

    def increase_query_count(self):
        """增加查询次数（同时增加月度和每日计数）"""
        now_month = datetime.now().strftime("%Y-%m")
        now_day = datetime.now().strftime("%Y-%m-%d")

        # 增加月度计数
        self._query_count[now_month] = self.get_current_month_query_count() + 1

        # 增加每日计数
        self._query_count[now_day] = self.get_current_day_query_count() + 1

        self._save_query_count()

    def is_prefix_allowed(self, prefix_to_check):
        """Checks if the given license plate prefix (e.g., '粤D') is allowed."""
        log_to_desktop(f"DEBUG AuthManager.is_prefix_allowed: Received prefix_to_check: '{prefix_to_check}'") # DEBUG
        allowed_prefixes_from_auth = self.get_allowed_prefixes() 
        log_to_desktop(f"DEBUG AuthManager.is_prefix_allowed: Raw allowed_prefixes_from_auth: {allowed_prefixes_from_auth}") # DEBUG

        if not allowed_prefixes_from_auth:
            log_to_desktop("DEBUG AuthManager.is_prefix_allowed: No specific prefixes defined in auth, allowing all.") # DEBUG
            return True
        
        normalized_prefix_to_check = prefix_to_check.strip().upper()
        log_to_desktop(f"DEBUG AuthManager.is_prefix_allowed: Normalized prefix_to_check: '{normalized_prefix_to_check}'") # DEBUG
        
        normalized_allowed_prefixes = [p.strip().upper() for p in allowed_prefixes_from_auth]
        log_to_desktop(f"DEBUG AuthManager.is_prefix_allowed: Normalized allowed_prefixes: {normalized_allowed_prefixes}") # DEBUG
        
        result = normalized_prefix_to_check in normalized_allowed_prefixes
        log_to_desktop(f"DEBUG AuthManager.is_prefix_allowed: Check result ('{normalized_prefix_to_check}' in {normalized_allowed_prefixes}): {result}") # DEBUG
        return result

    def is_province_allowed(self, province_code):
        """Checks if the given province (by letter code) is allowed."""
        allowed_short_names = self.get_allowed_provinces() # This returns list like ['粤', '京']
        
        # If allowed_short_names is empty, it means all provinces are allowed.
        if not allowed_short_names:
            return True
            
        # Convert the input province_code (e.g., 'gd') to its short name (e.g., '粤')
        province_short_name = PROVINCE_LETTER_CODE_TO_SHORT_NAME.get(province_code.lower())
        
        if province_short_name:
            # Check if the converted short name is in the list of allowed short names.
            # Also handle potential leading/trailing spaces in the allowed_short_names list from cloud data.
            return province_short_name in [s.strip() for s in allowed_short_names]
        else:
            # If the province_code cannot be converted (e.g., invalid code), consider it not allowed.
            return False

    def is_page_allowed(self, page_name):
        allowed = self.get_allowed_pages()
        return not allowed or page_name in allowed 