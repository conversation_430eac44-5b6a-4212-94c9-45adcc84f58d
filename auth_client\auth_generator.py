#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
授权码生成客户端
"""

import sys
import os
import json
import time
import base64
import sqlite3
import requests
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox, 
                           QSpinBox, QTextEdit, QGroupBox, QFormLayout, QMessageBox,
                           QListWidget, QListWidgetItem, QDialog, QFileDialog, QTabWidget,
                           QGridLayout, QFrame, QSplitter, QDateEdit, QInputDialog,
                           QListView)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QIcon

# 授权配置
AUTH_CONFIG = {
    "pre_str": "YiCheng",           # 机器码前缀
    "suf_str": "Tech",              # 机器码后缀
    "share_url": "https://note.youdao.com/s/L7eYSRyy",  # 有道云笔记分享链接
    "aes_key": "YiChengTech12345",  # AES密钥(16位)
    "aes_iv": "TechYiCheng12345",   # AES IV(16位)
    "request_url": "https://note.youdao.com/s/L7eYSRyy",
    "local_auth_file": "auth.dat"   # 本地授权文件
}

class AESCrypto:
    """AES加密解密工具"""
    def __init__(self, key, iv):
        self.key = key
        self.iv = iv
        
    def encrypt(self, data):
        """AES-CBC加密"""
        from Crypto.Cipher import AES
        from Crypto.Util.Padding import pad
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
        ct_bytes = cipher.encrypt(pad(data, AES.block_size))
        return ct_bytes
        
    def decrypt(self, data):
        """AES-CBC解密"""
        from Crypto.Cipher import AES
        from Crypto.Util.Padding import unpad
        
        cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
        pt = unpad(cipher.decrypt(data), AES.block_size)
        return pt.decode('utf-8')
        
    def encrypt_to_base64(self, data):
        """加密并转为Base64"""
        encrypted = self.encrypt(data)
        return base64.b64encode(encrypted).decode('utf-8')
        
    def decrypt_from_base64(self, b64_str):
        """从Base64解密"""
        encrypted = base64.b64decode(b64_str)
        return self.decrypt(encrypted)

class AuthDatabase:
    """授权数据库管理"""
    def __init__(self, db_path="auth_history.db"):
        self.db_path = db_path
        self.init_db()
        
    def init_db(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auth_history'")
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                # 创建新表
                cursor.execute('''
                CREATE TABLE auth_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    machine_code TEXT NOT NULL,
                    expire_time INTEGER NOT NULL,
                    allowed_pages TEXT,
                    allowed_provinces TEXT,
                    allowed_prefixes TEXT,
                    monthly_query_limit INTEGER DEFAULT 0,
                    daily_query_limit INTEGER DEFAULT 20,
                    remark TEXT,
                    auth_code TEXT NOT NULL,
                    create_time INTEGER NOT NULL,
                    update_time INTEGER NOT NULL,
                    tags TEXT,
                    is_active INTEGER DEFAULT 1
                )
                ''')
            else:
                # 检查并添加新列
                cursor.execute("PRAGMA table_info(auth_history)")
                columns = {col[1] for col in cursor.fetchall()}
                
                if 'update_time' not in columns:
                    cursor.execute('ALTER TABLE auth_history ADD COLUMN update_time INTEGER DEFAULT 0')
                if 'tags' not in columns:
                    cursor.execute('ALTER TABLE auth_history ADD COLUMN tags TEXT')
                if 'is_active' not in columns:
                    cursor.execute('ALTER TABLE auth_history ADD COLUMN is_active INTEGER DEFAULT 1')
                    # 将现有记录标记为活动状态
                    cursor.execute('UPDATE auth_history SET is_active = 1 WHERE is_active IS NULL')
                if 'daily_query_limit' not in columns:
                    cursor.execute('ALTER TABLE auth_history ADD COLUMN daily_query_limit INTEGER DEFAULT 20')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_machine_code ON auth_history(machine_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_create_time ON auth_history(create_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_is_active ON auth_history(is_active)')
            
            conn.commit()
            
        except Exception as e:
            print(f"初始化数据库失败: {str(e)}")
            conn.rollback()
            raise
        finally:
            conn.close()
        
    def add_auth_record(self, auth_data, auth_code):
        """添加或更新授权记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查是否存在相同机器码的记录
        cursor.execute('''
        SELECT id FROM auth_history 
        WHERE machine_code = ? AND is_active = 1
        ''', (auth_data["machine_code"],))
        
        existing_record = cursor.fetchone()
        current_time = int(time.time())
        
        if existing_record:
            # 将旧记录标记为非活动
            cursor.execute('''
            UPDATE auth_history SET is_active = 0, update_time = ?
            WHERE id = ?
            ''', (current_time, existing_record[0]))
        
        # 插入新记录
        cursor.execute('''
        INSERT INTO auth_history (
            machine_code, expire_time, allowed_pages, allowed_provinces,
            allowed_prefixes, monthly_query_limit, daily_query_limit, remark, auth_code,
            create_time, update_time, tags, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        ''', (
            auth_data["machine_code"],
            auth_data["expire_time"],
            json.dumps(auth_data["allowed_pages"], ensure_ascii=False),
            json.dumps(auth_data["allowed_provinces"], ensure_ascii=False),
            json.dumps(auth_data["allowed_prefixes"], ensure_ascii=False),
            auth_data["monthly_query_limit"],
            auth_data.get("daily_query_limit", 20),
            auth_data.get("remark", ""),
            auth_code,
            current_time,
            current_time,
            json.dumps(auth_data.get("tags", []), ensure_ascii=False)
        ))
        
        conn.commit()
        conn.close()
        
    def get_auth_records(self, search_text=None, active_only=True, page=1, page_size=50):
        """获取授权记录，支持搜索和分页"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 构建查询条件
        conditions = ["1=1"]
        params = []
        
        if active_only:
            conditions.append("is_active = 1")
            
        if search_text:
            conditions.append("(machine_code LIKE ? OR remark LIKE ? OR tags LIKE ?)")
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern, search_pattern])
            
        # 计算总记录数
        count_sql = f'''
        SELECT COUNT(*) FROM auth_history 
        WHERE {" AND ".join(conditions)}
        '''
        cursor.execute(count_sql, params)
        total_count = cursor.fetchone()[0]
        
        # 获取分页数据
        sql = f'''
        SELECT id, machine_code, expire_time, allowed_pages, allowed_provinces,
               allowed_prefixes, monthly_query_limit, daily_query_limit, remark, auth_code, create_time,
               update_time, tags, is_active
        FROM auth_history
        WHERE {" AND ".join(conditions)}
        ORDER BY create_time DESC
        LIMIT ? OFFSET ?
        '''
        
        params.extend([page_size, (page - 1) * page_size])
        cursor.execute(sql, params)
        
        records = []
        for row in cursor.fetchall():
            record = {
                "id": row[0],
                "auth_data": {
                    "machine_code": row[1],
                    "expire_time": row[2],
                    "allowed_pages": json.loads(row[3]),
                    "allowed_provinces": json.loads(row[4]),
                    "allowed_prefixes": json.loads(row[5]),
                    "monthly_query_limit": row[6],
                    "daily_query_limit": row[7] if row[7] is not None else 20,
                    "remark": row[8],
                    "tags": json.loads(row[12]) if row[12] else []
                },
                "auth_code": row[9],
                "create_time": row[10],
                "update_time": row[11],
                "is_active": bool(row[13])
            }
            records.append(record)
            
        conn.close()
        return records, total_count
        
    def delete_auth_record(self, record_id):
        """删除授权记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM auth_history WHERE id = ?", (record_id,))
        
        conn.commit()
        conn.close()
        
    def batch_delete_records(self, record_ids):
        """批量删除授权记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "DELETE FROM auth_history WHERE id IN (%s)" % 
            ",".join("?" * len(record_ids)), 
            record_ids
        )
        
        conn.commit()
        conn.close()
        
    def add_tags(self, record_id, tags):
        """添加标签"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT tags FROM auth_history WHERE id = ?", (record_id,))
        row = cursor.fetchone()
        if row:
            existing_tags = json.loads(row[0]) if row[0] else []
            new_tags = list(set(existing_tags + tags))
            
            cursor.execute(
                "UPDATE auth_history SET tags = ? WHERE id = ?",
                (json.dumps(new_tags, ensure_ascii=False), record_id)
            )
            
        conn.commit()
        conn.close()

class AuthGeneratorApp(QMainWindow):
    """授权码生成客户端"""
    
    def __init__(self):
        super().__init__()
        self.aes = AESCrypto(
            AUTH_CONFIG["aes_key"].encode('utf-8'),
            AUTH_CONFIG["aes_iv"].encode('utf-8')
        )
        self.db = AuthDatabase("auth_history.db")
        self.init_ui()
        self.load_auth_history()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("授权码生成工具 - 逸橙科技")
        self.setMinimumSize(1280, 800)
        
        # 设置图标
        self.setWindowIcon(QIcon("icon.ico"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建左右分割器
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.splitter)
        
        # 左侧面板 - 授权历史
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 历史记录标题和搜索区域
        history_header = QWidget()
        history_header_layout = QHBoxLayout(history_header)
        history_header_layout.setContentsMargins(0, 0, 0, 0)
        
        history_label = QLabel("授权历史")
        history_label.setFont(QFont("微软雅黑", 12, QFont.Weight.Bold))
        history_header_layout.addWidget(history_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索机器码/备注/标签...")
        self.search_edit.textChanged.connect(self.on_search_changed)
        history_header_layout.addWidget(self.search_edit)
        
        left_layout.addWidget(history_header)
        
        # 历史记录过滤器
        filter_widget = QWidget()
        filter_layout = QHBoxLayout(filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)
        
        self.show_inactive_cb = QCheckBox("显示历史版本")
        self.show_inactive_cb.stateChanged.connect(self.load_auth_history)
        filter_layout.addWidget(self.show_inactive_cb)
        
        self.page_spin = QSpinBox()
        self.page_spin.setRange(1, 1)
        self.page_spin.valueChanged.connect(self.load_auth_history)
        filter_layout.addWidget(self.page_spin)
        
        self.total_pages_label = QLabel("/ 1页")
        filter_layout.addWidget(self.total_pages_label)
        
        left_layout.addWidget(filter_widget)
        
        # 历史记录列表
        self.history_list = QListWidget()
        self.history_list.currentItemChanged.connect(self.on_history_item_selected)
        self.history_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        left_layout.addWidget(self.history_list)
        
        # 历史记录操作按钮
        history_btn_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.load_auth_history)
        history_btn_layout.addWidget(refresh_btn)
        
        delete_btn = QPushButton("删除")
        delete_btn.clicked.connect(self.delete_history_item)
        history_btn_layout.addWidget(delete_btn)
        
        batch_delete_btn = QPushButton("批量删除")
        batch_delete_btn.clicked.connect(self.batch_delete_items)
        history_btn_layout.addWidget(batch_delete_btn)
        
        add_tag_btn = QPushButton("添加标签")
        add_tag_btn.clicked.connect(self.add_tag_to_items)
        history_btn_layout.addWidget(add_tag_btn)
        
        left_layout.addLayout(history_btn_layout)
        
        # 右侧面板 - 授权生成
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 标题
        title_label = QLabel("授权码生成")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(title_label)
        
        # 表单区域
        form_group = QGroupBox("授权信息")
        form_layout = QFormLayout()
        
        # 机器码
        form_layout.addRow(QLabel("机器码:"))
        machine_code_layout = QHBoxLayout()
        
        self.machine_code_edit = QLineEdit()
        self.machine_code_edit.setPlaceholderText("输入用户的机器码")
        machine_code_layout.addWidget(self.machine_code_edit, 4)
        
        self.machine_code_combo = QComboBox()
        self.machine_code_combo.setEditable(True)
        self.machine_code_combo.setPlaceholderText("选择历史机器码")
        self.machine_code_combo.currentTextChanged.connect(self.on_machine_code_selected)
        machine_code_layout.addWidget(self.machine_code_combo, 2)
        
        form_layout.addRow(machine_code_layout)
        
        # 授权期限
        expire_layout = QHBoxLayout()
        self.expire_type_combo = QComboBox()
        self.expire_type_combo.addItems(["永久授权", "临时授权"])
        self.expire_type_combo.currentIndexChanged.connect(self.on_expire_type_changed)
        expire_layout.addWidget(self.expire_type_combo)
        
        # 常用天数下拉框
        self.expire_preset_combo = QComboBox()
        self.expire_preset_combo.addItems(["3天", "7天", "30天", "90天", "180天", "365天", "730天", "1095天"])
        self.expire_preset_combo.currentTextChanged.connect(self.on_expire_preset_changed)
        expire_layout.addWidget(self.expire_preset_combo)
        
        self.expire_days_spin = QSpinBox()
        self.expire_days_spin.setRange(1, 3650)
        self.expire_days_spin.setValue(365)
        self.expire_days_spin.setSuffix(" 天")
        self.expire_days_spin.valueChanged.connect(self.on_days_changed)
        expire_layout.addWidget(self.expire_days_spin)
        
        self.expire_date_edit = QDateEdit()
        self.expire_date_edit.setDate(QDate.currentDate().addDays(365))
        self.expire_date_edit.setCalendarPopup(True)
        self.expire_date_edit.dateChanged.connect(self.on_date_changed)
        expire_layout.addWidget(self.expire_date_edit)
        
        form_layout.addRow(QLabel("授权期限:"), expire_layout)
        self.on_expire_type_changed(0)  # 初始化显示
        
        # 功能授权
        form_layout.addRow(QLabel("功能授权:"))
        
        pages_layout = QGridLayout()
        self.page_checkboxes = {}
        pages = ["违章查询", "违章分析", "车辆管理", "租金管理", "租金分析", "微信助手", "违章转移"]
        
        for i, page in enumerate(pages):
            row, col = i // 4, i % 4
            cb = QCheckBox(page)
            cb.setChecked(True)
            self.page_checkboxes[page] = cb
            pages_layout.addWidget(cb, row, col)
        
        # 添加快速选择按钮
        pages_btn_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(lambda: self.batch_select_pages(True))
        pages_btn_layout.addWidget(select_all_btn)
        
        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(lambda: self.batch_select_pages(False))
        pages_btn_layout.addWidget(select_none_btn)
        
        # Adjust row position and column span for the buttons
        button_row = (len(pages) - 1) // 4 + 1
        pages_layout.addLayout(pages_btn_layout, button_row, 0, 1, 4)
        form_layout.addRow(pages_layout)
        
        # 省份和车牌前缀区域 - 左右布局
        province_prefix_layout = QHBoxLayout()
        
        # 省份授权 - 左侧
        province_group = QGroupBox("省份授权")
        province_layout = QVBoxLayout(province_group)
        
        # 省份按钮矩阵
        province_grid = QGridLayout()
        province_grid.setSpacing(1)  # 设置更小的间距
        
        # 添加所有省份按钮
        provinces = [
            "京", "津", "冀", "晋", "蒙",
            "辽", "吉", "黑", "沪", "苏",
            "浙", "皖", "闽", "赣", "鲁",
            "豫", "鄂", "湘", "粤", "桂",
            "琼", "渝", "川", "贵", "云",
            "藏", "陕", "甘", "青", "宁",
            "新"
        ]
        
        self.province_buttons = {}
        # 按照每行8个按钮排列
        for i, province in enumerate(provinces):
            row, col = i // 8, i % 8
            btn = QPushButton(province)
            btn.setFixedSize(35, 35)  # 稍微减小按钮尺寸
            btn.setCheckable(True)
            self.province_buttons[province] = btn
            province_grid.addWidget(btn, row, col)
        
        # 设置省份按钮的样式表，使其更加紧凑
        for btn in self.province_buttons.values():
            btn.setStyleSheet("QPushButton { padding: 1px; margin: 0px; font-size: 12px; }")
        
        province_layout.addLayout(province_grid)
        
        # 省份快速选择按钮
        province_btn_layout = QHBoxLayout()
        province_btn_layout.setSpacing(2)  # 设置更小的间距
        
        select_all_provinces_btn = QPushButton("全选")
        select_all_provinces_btn.clicked.connect(lambda: self.batch_select_province_buttons(True))
        select_all_provinces_btn.setFixedHeight(25)  # 减小按钮高度
        province_btn_layout.addWidget(select_all_provinces_btn)
        
        select_none_provinces_btn = QPushButton("全不选")
        select_none_provinces_btn.clicked.connect(lambda: self.batch_select_province_buttons(False))
        select_none_provinces_btn.setFixedHeight(25)  # 减小按钮高度
        province_btn_layout.addWidget(select_none_provinces_btn)
        
        # 区域选择按钮
        region_combo = QComboBox()
        region_combo.addItems(["选择区域", "华南地区", "华东地区", "华北地区", "西南地区", "东北地区", "西北地区", "华中地区"])
        region_combo.currentTextChanged.connect(self.on_region_button_selected)
        region_combo.setFixedHeight(25)  # 减小下拉框高度
        province_btn_layout.addWidget(region_combo)
        
        province_layout.addLayout(province_btn_layout)
        
        # 车牌前缀授权 - 右侧
        prefix_group = QGroupBox("车牌前缀")
        prefix_layout = QVBoxLayout(prefix_group)
        
        # 字母按钮矩阵
        prefix_grid = QGridLayout()
        prefix_grid.setSpacing(1)  # 设置更小的间距
        
        # 添加所有字母按钮
        self.prefix_buttons = {}
        # 按照每行9个按钮排列
        for i in range(26):
            letter = chr(65 + i)  # A-Z
            row, col = i // 9, i % 9
            btn = QPushButton(letter)
            btn.setFixedSize(35, 35)  # 稍微减小按钮尺寸
            btn.setCheckable(True)
            self.prefix_buttons[letter] = btn
            prefix_grid.addWidget(btn, row, col)
        
        # 设置字母按钮的样式表，使其更加紧凑
        for btn in self.prefix_buttons.values():
            btn.setStyleSheet("QPushButton { padding: 1px; margin: 0px; font-size: 12px; }")
        
        prefix_layout.addLayout(prefix_grid)
        
        # 字母快速选择按钮
        prefix_btn_layout = QHBoxLayout()
        prefix_btn_layout.setSpacing(2)  # 设置更小的间距
        
        select_all_prefix_btn = QPushButton("全选")
        select_all_prefix_btn.clicked.connect(lambda: self.batch_select_prefix_buttons(True))
        select_all_prefix_btn.setFixedHeight(25)  # 减小按钮高度
        prefix_btn_layout.addWidget(select_all_prefix_btn)
        
        select_none_prefix_btn = QPushButton("全不选")
        select_none_prefix_btn.clicked.connect(lambda: self.batch_select_prefix_buttons(False))
        select_none_prefix_btn.setFixedHeight(25)  # 减小按钮高度
        prefix_btn_layout.addWidget(select_none_prefix_btn)
        
        prefix_layout.addLayout(prefix_btn_layout)
        
        # 添加到左右布局
        province_prefix_layout.addWidget(province_group)
        province_prefix_layout.addWidget(prefix_group)
        
        # 添加到表单
        form_layout.addRow(province_prefix_layout)
        
        # 查询次数限制
        form_layout.addRow(QLabel("查询次数限制:"))

        # 每月查询次数
        monthly_limit_layout = QHBoxLayout()
        monthly_limit_layout.addWidget(QLabel("每月:"))

        self.query_limit_spin = QSpinBox()
        self.query_limit_spin.setRange(0, 1000000)
        self.query_limit_spin.setValue(0)
        self.query_limit_spin.setSpecialValueText("不限制")
        monthly_limit_layout.addWidget(self.query_limit_spin)

        self.query_limit_combo = QComboBox()
        self.query_limit_combo.addItems(["不限制", "100次/月", "500次/月", "1000次/月", "5000次/月", "10000次/月"])
        self.query_limit_combo.currentTextChanged.connect(self.on_query_limit_preset_changed)
        monthly_limit_layout.addWidget(self.query_limit_combo)

        form_layout.addRow(monthly_limit_layout)

        # 每日查询次数
        daily_limit_layout = QHBoxLayout()
        daily_limit_layout.addWidget(QLabel("每日:"))

        self.daily_query_limit_spin = QSpinBox()
        self.daily_query_limit_spin.setRange(1, 1000)
        self.daily_query_limit_spin.setValue(20)
        self.daily_query_limit_spin.setSuffix(" 次")
        daily_limit_layout.addWidget(self.daily_query_limit_spin)

        self.daily_limit_combo = QComboBox()
        self.daily_limit_combo.addItems(["10次/日", "20次/日", "50次/日", "100次/日", "200次/日", "500次/日"])
        self.daily_limit_combo.currentTextChanged.connect(self.on_daily_limit_preset_changed)
        daily_limit_layout.addWidget(self.daily_limit_combo)

        form_layout.addRow(daily_limit_layout)
        
        # 备注
        form_layout.addRow(QLabel("备注:"))
        
        remark_layout = QHBoxLayout()
        self.remark_edit = QLineEdit()
        self.remark_edit.setPlaceholderText("输入备注信息，如客户名称等")
        remark_layout.addWidget(self.remark_edit)
        
        self.remark_combo = QComboBox()
        self.remark_combo.setEditable(True)
        self.remark_combo.setPlaceholderText("选择历史备注")
        self.remark_combo.currentTextChanged.connect(self.on_remark_selected)
        remark_layout.addWidget(self.remark_combo)
        
        form_layout.addRow(remark_layout)
        
        form_group.setLayout(form_layout)
        right_layout.addWidget(form_group)
        
        # 生成的授权码
        right_layout.addWidget(QLabel("生成的授权码:"))
        
        self.auth_code_text = QTextEdit()
        self.auth_code_text.setReadOnly(True)
        self.auth_code_text.setFixedHeight(80)
        right_layout.addWidget(self.auth_code_text)
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        
        generate_btn = QPushButton("生成授权码")
        generate_btn.clicked.connect(self.generate_auth_code)
        btn_layout.addWidget(generate_btn)
        
        copy_btn = QPushButton("复制授权码")
        copy_btn.clicked.connect(self.copy_auth_code)
        btn_layout.addWidget(copy_btn)
        
        save_btn = QPushButton("保存到文件")
        save_btn.clicked.connect(self.save_to_file)
        btn_layout.addWidget(save_btn)
        
        upload_btn = QPushButton("上传到云端")
        upload_btn.clicked.connect(self.upload_to_cloud)
        btn_layout.addWidget(upload_btn)
        
        right_layout.addLayout(btn_layout)
        
        # 添加到分割器
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)
        
    def on_expire_type_changed(self, index):
        """处理授权期限类型变化"""
        if index == 0:  # 永久授权
            self.expire_preset_combo.setVisible(False)
            self.expire_days_spin.setVisible(False)
            self.expire_date_edit.setVisible(False)
        else:  # 临时授权
            self.expire_preset_combo.setVisible(True)
            self.expire_days_spin.setVisible(True)
            self.expire_date_edit.setVisible(True)
            
    def on_expire_preset_changed(self, text):
        """处理授权期限预设变化"""
        if not text or text == "自定义":
            return
            
        # 禁用信号以避免循环调用
        self.expire_days_spin.blockSignals(True)
        self.expire_date_edit.blockSignals(True)
        
        days = int(text.replace("天", ""))
        self.expire_days_spin.setValue(days)
        self.expire_date_edit.setDate(QDate.currentDate().addDays(days))
        
        # 重新启用信号
        self.expire_days_spin.blockSignals(False)
        self.expire_date_edit.blockSignals(False)

    def on_days_changed(self, days):
        """天数改变时更新日期选择器"""
        # 禁用信号以避免循环调用
        self.expire_date_edit.blockSignals(True)
        self.expire_preset_combo.blockSignals(True)
        
        self.expire_date_edit.setDate(QDate.currentDate().addDays(days))
        
        # 检查是否匹配预设值
        day_str = f"{days}天"
        index = self.expire_preset_combo.findText(day_str)
        if index >= 0:
            self.expire_preset_combo.setCurrentIndex(index)
        
        # 重新启用信号
        self.expire_date_edit.blockSignals(False)
        self.expire_preset_combo.blockSignals(False)

    def on_date_changed(self, date):
        """日期改变时更新天数"""
        # 禁用信号以避免循环调用
        self.expire_days_spin.blockSignals(True)
        self.expire_preset_combo.blockSignals(True)
        
        # 计算从当前日期到选择日期的天数
        days = QDate.currentDate().daysTo(date)
        if days >= 1:
            self.expire_days_spin.setValue(days)
            
            # 检查是否匹配预设值
            day_str = f"{days}天"
            index = self.expire_preset_combo.findText(day_str)
            if index >= 0:
                self.expire_preset_combo.setCurrentIndex(index)
        
        # 重新启用信号
        self.expire_days_spin.blockSignals(False)
        self.expire_preset_combo.blockSignals(False)
        
    def on_machine_code_selected(self, text):
        """处理机器码选择"""
        if text:
            self.machine_code_edit.setText(text)
            
    def on_remark_selected(self, text):
        """处理备注选择"""
        if text:
            self.remark_edit.setText(text)
            
    def batch_select_pages(self, select_all):
        """批量选择功能页面"""
        for cb in self.page_checkboxes.values():
            cb.setChecked(select_all)
        
    def generate_auth_code(self):
        """生成授权码"""
        machine_code = self.machine_code_edit.text().strip()
        if not machine_code:
            QMessageBox.warning(self, "提示", "请输入机器码")
            return
            
        # 获取授权期限
        if self.expire_type_combo.currentIndex() == 0:  # 永久授权
            expire_days = -1
            expire_time = -1  # 永久有效
            expire_str = "永久"
        else:  # 临时授权
            expire_days = self.expire_days_spin.value()
            expire_time = int(time.time() + expire_days * 24 * 3600)
            expire_str = datetime.fromtimestamp(expire_time).strftime("%Y-%m-%d")
            
        # 获取允许的页面
        allowed_pages = []
        for page, cb in self.page_checkboxes.items():
            if cb.isChecked():
                allowed_pages.append(page)
                
        # 获取允许的省份
        allowed_provinces = []
        for province, btn in self.province_buttons.items():
            if btn.isChecked():
                allowed_provinces.append(province)
            
        # 获取允许的车牌前缀
        allowed_prefixes = []
        for letter, btn in self.prefix_buttons.items():
            if btn.isChecked():
                allowed_prefixes.append(letter)
            
        # 获取查询次数限制
        monthly_query_limit = self.query_limit_spin.value()
        daily_query_limit = self.daily_query_limit_spin.value()

        # 获取备注
        remark = self.remark_edit.text().strip()

        # 构建授权数据
        auth_data = {
            "machine_code": machine_code,
            "expire_time": expire_time,
            "allowed_pages": allowed_pages,
            "allowed_provinces": allowed_provinces,
            "allowed_prefixes": allowed_prefixes,
            "monthly_query_limit": monthly_query_limit,
            "daily_query_limit": daily_query_limit,
            "remark": f"{remark} - {expire_str} - 每日{daily_query_limit}次",  # 备注中包含到期日期和每日限制
            "create_time": int(time.time()),
            "version": "1.0"
        }
        
        # 转换为JSON
        json_data = json.dumps(auth_data, ensure_ascii=False)
        
        # 加密并Base64编码
        auth_code = self.aes.encrypt_to_base64(json_data)
        
        # 显示授权码
        self.auth_code_text.setText(auth_code)
        
        # 保存到数据库
        self.db.add_auth_record(auth_data, auth_code)
        
        # 刷新历史记录列表
        self.load_auth_history()
        
        QMessageBox.information(self, "成功", "授权码生成成功")
        
    def copy_auth_code(self):
        """复制授权码"""
        auth_code = self.auth_code_text.toPlainText()
        if not auth_code:
            QMessageBox.warning(self, "提示", "请先生成授权码")
            return
            
        remark = self.remark_edit.text().strip()
        machine_code = self.machine_code_edit.text().strip()
        
        # 复制的格式中不再包含截至日期
        formatted_text = f"//{remark}\n{machine_code};{auth_code}\n"
            
        QApplication.clipboard().setText(formatted_text)
        QMessageBox.information(self, "成功", "授权码已复制到剪贴板")
        
    def save_to_file(self):
        """保存授权码到文件"""
        auth_code = self.auth_code_text.toPlainText()
        if not auth_code:
            QMessageBox.warning(self, "提示", "请先生成授权码")
            return
            
        # 获取到期日期
        if self.expire_type_combo.currentIndex() == 0:  # 永久授权
            expire_str = "永久"
        else:
            expire_time = int(time.time() + self.expire_days_spin.value() * 24 * 3600)
            expire_str = datetime.fromtimestamp(expire_time).strftime("%Y-%m-%d")
        
        # 构建文件名，只包含到期日期
        file_name = f"授权码-{expire_str}.txt"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存授权码", file_name, "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"授权码: {auth_code}\n")
                QMessageBox.information(self, "成功", f"授权码已保存到 {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "保存失败", f"保存授权码失败: {str(e)}")
                
    def upload_to_cloud(self):
        """上传授权码到云端"""
        auth_code = self.auth_code_text.toPlainText()
        if not auth_code:
            QMessageBox.warning(self, "提示", "请先生成授权码")
            return
            
        # 这里实现上传到有道云笔记的逻辑
        # 由于有道云笔记API需要登录认证，这里使用一个简化的对话框提示用户手动上传
        dialog = CloudUploadDialog(auth_code, self)
        dialog.exec()
            
    def load_auth_history(self):
        """加载授权历史"""
        try:
            search_text = self.search_edit.text().strip()
            active_only = not self.show_inactive_cb.isChecked()
            current_page = self.page_spin.value()
            
            records, total_count = self.db.get_auth_records(
                search_text=search_text,
                active_only=active_only,
                page=current_page
            )
            
            # 更新分页信息
            total_pages = (total_count + 49) // 50  # 每页50条记录
            self.page_spin.setRange(1, max(1, total_pages))
            self.total_pages_label.setText(f"/ {total_pages}页")
            
            # 清空列表
            self.history_list.clear()
            
            # 收集机器码和备注用于下拉框
            machine_codes = set()
            remarks = set()
            
            # 添加历史记录
            for item in records:
                auth_data = item["auth_data"]
                create_time = datetime.fromtimestamp(item["create_time"]).strftime("%Y-%m-%d %H:%M:%S")
                
                # 收集机器码和备注
                machine_codes.add(auth_data["machine_code"])
                if auth_data.get("remark"):
                    remarks.add(auth_data["remark"])
                
                # 获取过期时间
                expire_time = auth_data["expire_time"]
                if expire_time == -1:
                    expire_str = "永久"
                else:
                    expire_str = datetime.fromtimestamp(expire_time).strftime("%Y-%m-%d")
                
                # 获取标签
                tags = auth_data.get("tags", [])
                tags_str = f" [{', '.join(tags)}]" if tags else ""
                
                # 获取备注
                remark = auth_data.get("remark", "")
                remark_str = f" - {remark}" if remark else ""
                
                # 创建列表项
                status = "" if item["is_active"] else "[历史] "
                list_item = QListWidgetItem(
                    f"{status}{create_time} - {auth_data['machine_code']} - {expire_str}{remark_str}{tags_str}"
                )
                
                if not item["is_active"]:
                    list_item.setForeground(Qt.GlobalColor.gray)
                
                # 存储完整数据
                list_item.setData(Qt.ItemDataRole.UserRole, item)
                
                self.history_list.addItem(list_item)
                
            # 更新下拉框
            self.update_combo_boxes(machine_codes, remarks)
                
        except Exception as e:
            print(f"加载历史记录失败: {str(e)}")
            
    def update_combo_boxes(self, machine_codes, remarks):
        """更新下拉框选项"""
        # 保存当前选中的值
        current_machine_code = self.machine_code_combo.currentText()
        current_remark = self.remark_combo.currentText()
        
        # 更新机器码下拉框
        self.machine_code_combo.clear()
        self.machine_code_combo.addItem("")  # 空选项
        for code in sorted(machine_codes):
            self.machine_code_combo.addItem(code)
            
        # 更新备注下拉框
        self.remark_combo.clear()
        self.remark_combo.addItem("")  # 空选项
        for remark in sorted(remarks):
            self.remark_combo.addItem(remark)
            
        # 恢复选中的值
        if current_machine_code:
            index = self.machine_code_combo.findText(current_machine_code)
            if index >= 0:
                self.machine_code_combo.setCurrentIndex(index)
                
        if current_remark:
            index = self.remark_combo.findText(current_remark)
            if index >= 0:
                self.remark_combo.setCurrentIndex(index)
        
    def on_search_changed(self):
        """处理搜索文本变化"""
        self.page_spin.setValue(1)  # 重置到第一页
        self.load_auth_history()
        
    def batch_delete_items(self):
        """批量删除选中的历史记录"""
        selected_items = self.history_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "提示", "请先选择要删除的历史记录")
            return
            
        reply = QMessageBox.question(
            self, "确认删除", f"确定要删除选中的 {len(selected_items)} 条历史记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
            
        try:
            record_ids = []
            for item in selected_items:
                item_data = item.data(Qt.ItemDataRole.UserRole)
                record_ids.append(item_data["id"])
                
            self.db.batch_delete_records(record_ids)
            self.load_auth_history()
            
            QMessageBox.information(self, "成功", "历史记录已删除")
            
        except Exception as e:
            QMessageBox.warning(self, "删除失败", f"删除历史记录失败: {str(e)}")
            
    def add_tag_to_items(self):
        """为选中的记录添加标签"""
        selected_items = self.history_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "提示", "请先选择要添加标签的历史记录")
            return
            
        tag, ok = QInputDialog.getText(
            self, "添加标签", 
            "请输入要添加的标签（多个标签用逗号分隔）:",
            QLineEdit.EchoMode.Normal
        )
        
        if not ok or not tag.strip():
            return
            
        try:
            tags = [t.strip() for t in tag.split(",") if t.strip()]
            for item in selected_items:
                item_data = item.data(Qt.ItemDataRole.UserRole)
                self.db.add_tags(item_data["id"], tags)
                
            self.load_auth_history()
            
            QMessageBox.information(self, "成功", "标签添加成功")
            
        except Exception as e:
            QMessageBox.warning(self, "添加失败", f"添加标签失败: {str(e)}")
        
    def on_history_item_selected(self, current, previous):
        """处理历史记录选择"""
        if not current:
            return
            
        # 获取选中的历史记录
        item_data = current.data(Qt.ItemDataRole.UserRole)
        auth_data = item_data["auth_data"]
        auth_code = item_data["auth_code"]
        
        # 填充表单
        self.machine_code_edit.setText(auth_data["machine_code"])
        
        # 设置授权期限
        if auth_data["expire_time"] == -1:
            self.expire_type_combo.setCurrentIndex(0)  # 永久授权
        else:
            self.expire_type_combo.setCurrentIndex(1)  # 临时授权
            days_left = max(0, (auth_data["expire_time"] - time.time()) // (24 * 3600))
            self.expire_days_spin.setValue(int(days_left))
            self.expire_date_edit.setDate(QDate.fromString(
                datetime.fromtimestamp(auth_data["expire_time"]).strftime("%Y-%m-%d"),
                "yyyy-MM-dd"
            ))
            
        # 设置功能授权
        allowed_pages = auth_data["allowed_pages"]
        for page, cb in self.page_checkboxes.items():
            cb.setChecked(page in allowed_pages)
            
        # 设置省份授权
        allowed_provinces = auth_data["allowed_provinces"]
        self.batch_select_province_buttons(False)  # 先清除所有选择
        for province, btn in self.province_buttons.items():
            btn.setChecked(province in allowed_provinces)
        
        # 设置车牌前缀
        allowed_prefixes = auth_data["allowed_prefixes"]
        self.batch_select_prefix_buttons(False)  # 先清除所有选择
        for letter, btn in self.prefix_buttons.items():
            btn.setChecked(letter in allowed_prefixes)
        
        # 设置查询次数限制
        self.query_limit_spin.setValue(auth_data["monthly_query_limit"])
        self.daily_query_limit_spin.setValue(auth_data.get("daily_query_limit", 20))

        # 设置备注
        self.remark_edit.setText(auth_data.get("remark", ""))
        
        # 显示授权码
        self.auth_code_text.setText(auth_code)
        
    def delete_history_item(self):
        """删除历史记录"""
        current_item = self.history_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请先选择要删除的历史记录")
            return
            
        reply = QMessageBox.question(
            self, "确认删除", "确定要删除选中的历史记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
            
        try:
            # 获取要删除的记录
            item_data = current_item.data(Qt.ItemDataRole.UserRole)
            
            # 删除记录
            self.db.delete_auth_record(item_data["id"])
                
            # 刷新列表
            self.load_auth_history()
            
            QMessageBox.information(self, "成功", "历史记录已删除")
            
        except Exception as e:
            QMessageBox.warning(self, "删除失败", f"删除历史记录失败: {str(e)}")

    def resizeEvent(self, event):
        """处理窗口大小改变事件"""
        super().resizeEvent(event)
        # 保持5:5的分割比例
        total_width = self.width()
        left_width = int(total_width * 0.5)
        right_width = int(total_width * 0.5)
        self.splitter.setSizes([left_width, right_width])

    def batch_select_province_buttons(self, select_all):
        """批量选择省份按钮"""
        for btn in self.province_buttons.values():
            btn.setChecked(select_all)
            
    def batch_select_prefix_buttons(self, select_all):
        """批量选择字母按钮"""
        for btn in self.prefix_buttons.values():
            btn.setChecked(select_all)
            
    def on_region_button_selected(self, region):
        """处理区域选择"""
        if region == "选择区域":
            return
            
        # 先取消所有选择
        self.batch_select_province_buttons(False)
        
        # 根据区域选择省份
        region_provinces = {
            "华南地区": ["粤", "桂", "琼"],
            "华东地区": ["沪", "苏", "浙", "皖", "闽", "赣", "鲁"],
            "华北地区": ["京", "津", "冀", "晋", "蒙"],
            "西南地区": ["川", "贵", "云", "渝", "藏"],
            "东北地区": ["辽", "吉", "黑"],
            "西北地区": ["陕", "甘", "宁", "青", "新"],
            "华中地区": ["豫", "鄂", "湘"]
        }
        
        # 选择对应区域的省份
        if region in region_provinces:
            for province in region_provinces[region]:
                if province in self.province_buttons:
                    self.province_buttons[province].setChecked(True)
                    
    def on_query_limit_preset_changed(self, text):
        """处理月度查询次数预设变化"""
        if text == "不限制":
            self.query_limit_spin.setValue(0)
        elif "次/月" in text:
            limit = int(text.split("次")[0])
            self.query_limit_spin.setValue(limit)

    def on_daily_limit_preset_changed(self, text):
        """处理每日查询次数预设变化"""
        if "次/日" in text:
            limit = int(text.split("次")[0])
            self.daily_query_limit_spin.setValue(limit)

class CloudUploadDialog(QDialog):
    """云端上传对话框"""
    
    def __init__(self, auth_code, parent=None):
        super().__init__(parent)
        self.auth_code = auth_code
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("上传到云端")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("上传授权码到有道云笔记")
        title_label.setFont(QFont("微软雅黑", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel(
            "由于有道云笔记API需要登录认证，请按照以下步骤手动上传授权码：\n\n"
            "1. 复制下方的授权码\n"
            "2. 打开有道云笔记网页版\n"
            "3. 登录您的账号\n"
            "4. 打开或创建用于存储授权码的笔记\n"
            "5. 将授权码粘贴到笔记中\n"
            "6. 保存笔记\n"
            "7. 确保笔记已分享，分享链接为配置文件中的链接"
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 授权码
        layout.addWidget(QLabel("授权码:"))
        
        auth_text = QTextEdit()
        auth_text.setReadOnly(True)
        auth_text.setText(self.auth_code)
        layout.addWidget(auth_text)
        
        # 链接
        layout.addWidget(QLabel("有道云笔记分享链接:"))
        
        link_edit = QLineEdit(AUTH_CONFIG["share_url"])
        link_edit.setReadOnly(True)
        layout.addWidget(link_edit)
        
        # 按钮
        btn_layout = QHBoxLayout()
        
        copy_btn = QPushButton("复制授权码")
        copy_btn.clicked.connect(lambda: self.copy_text(self.auth_code))
        btn_layout.addWidget(copy_btn)
        
        copy_link_btn = QPushButton("复制链接")
        copy_link_btn.clicked.connect(lambda: self.copy_text(AUTH_CONFIG["share_url"]))
        btn_layout.addWidget(copy_link_btn)
        
        open_btn = QPushButton("打开有道云笔记")
        open_btn.clicked.connect(self.open_youdao)
        btn_layout.addWidget(open_btn)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        btn_layout.addWidget(close_btn)
        
        layout.addLayout(btn_layout)
        
    def copy_text(self, text):
        """复制文本"""
        QApplication.clipboard().setText(text)
        QMessageBox.information(self, "成功", "文本已复制到剪贴板")
        
    def open_youdao(self):
        """打开有道云笔记"""
        import webbrowser
        webbrowser.open("https://note.youdao.com/")

def main():
    app = QApplication(sys.argv)
    window = AuthGeneratorApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 