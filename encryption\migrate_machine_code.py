#!/usr/bin/env python3
"""
机器码迁移工具

用于帮助现有用户从旧的机器码算法迁移到新的稳定算法
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from encryption.machine_code import MachineCodeGenerator
from encryption.auth_manager import AuthManager

class MachineCodeMigrator:
    def __init__(self):
        self.generator = MachineCodeGenerator()
        
    def get_old_machine_code(self):
        """获取旧版本的机器码（使用MD5算法）"""
        import hashlib
        try:
            # 使用旧的算法生成机器码
            combine_str = (
                self.generator.pre_str + 
                self.generator.get_stable_mac_address() + 
                self.generator.get_cpu_serial() + 
                self.generator.get_disk_serial() + 
                self.generator.get_board_serial() + 
                self.generator.suf_str
            )
            # 使用MD5（旧算法）
            return hashlib.md5(combine_str.encode("utf-8")).hexdigest().upper()
        except Exception as e:
            print(f"获取旧机器码失败: {e}")
            return None
    
    def get_new_machine_code(self):
        """获取新版本的机器码（使用SHA-256算法和系统UUID）"""
        return self.generator.get_machine_code(use_cache=False)
    
    def compare_machine_codes(self):
        """比较新旧机器码"""
        print("=" * 60)
        print("机器码比较")
        print("=" * 60)
        
        old_code = self.get_old_machine_code()
        new_code = self.get_new_machine_code()
        
        print(f"旧机器码 (MD5): {old_code}")
        print(f"新机器码 (SHA256): {new_code}")
        
        if old_code == new_code:
            print("✅ 机器码未发生变化")
            return False
        else:
            print("⚠️  机器码已发生变化")
            return True
    
    def create_migration_report(self):
        """创建迁移报告"""
        report = {
            "migration_time": datetime.now().isoformat(),
            "old_machine_code": self.get_old_machine_code(),
            "new_machine_code": self.get_new_machine_code(),
            "hardware_components": self.generator.get_hardware_components(use_cache=False),
            "migration_reason": "Upgrade to stable machine code algorithm"
        }
        
        # 保存报告到桌面
        try:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            report_file = os.path.join(desktop, "machine_code_migration_report.json")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"迁移报告已保存到: {report_file}")
            return report_file
        except Exception as e:
            print(f"保存迁移报告失败: {e}")
            return None
    
    def backup_auth_data(self):
        """备份现有的授权数据"""
        try:
            auth_manager = AuthManager()
            
            # 检查是否有本地授权文件
            if os.path.exists(auth_manager._user_local_auth_file):
                backup_file = auth_manager._user_local_auth_file + ".backup"
                
                # 创建备份
                import shutil
                shutil.copy2(auth_manager._user_local_auth_file, backup_file)
                print(f"授权数据已备份到: {backup_file}")
                return backup_file
            else:
                print("未找到本地授权文件，无需备份")
                return None
        except Exception as e:
            print(f"备份授权数据失败: {e}")
            return None
    
    def migrate(self, force=False):
        """执行迁移"""
        print("=" * 80)
        print("机器码迁移工具")
        print("=" * 80)
        print(f"迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. 比较机器码
        code_changed = self.compare_machine_codes()
        
        if not code_changed and not force:
            print("\n机器码未发生变化，无需迁移")
            return True
        
        print("\n开始迁移过程...")
        
        # 2. 备份授权数据
        print("\n步骤 1: 备份现有授权数据")
        backup_file = self.backup_auth_data()
        
        # 3. 清除旧的机器码缓存
        print("\n步骤 2: 清除旧的机器码缓存")
        self.generator.clear_cache()
        print("缓存已清除")
        
        # 4. 生成新的机器码
        print("\n步骤 3: 生成新的稳定机器码")
        new_code = self.generator.get_machine_code(use_cache=True)
        print(f"新机器码: {new_code}")
        
        # 5. 创建迁移报告
        print("\n步骤 4: 创建迁移报告")
        report_file = self.create_migration_report()
        
        # 6. 清除本地授权（需要重新授权）
        print("\n步骤 5: 清除本地授权数据（需要重新获取授权）")
        try:
            auth_manager = AuthManager()
            if auth_manager.delete_local_auth_file():
                print("本地授权数据已清除")
            else:
                print("清除本地授权数据失败")
        except Exception as e:
            print(f"清除授权数据时出错: {e}")
        
        print("\n" + "=" * 80)
        print("迁移完成！")
        print("=" * 80)
        print("📋 迁移总结:")
        print(f"   • 新机器码: {new_code}")
        print(f"   • 备份文件: {backup_file if backup_file else '无'}")
        print(f"   • 迁移报告: {report_file if report_file else '无'}")
        print("\n⚠️  重要提醒:")
        print("   • 机器码已更新，需要重新获取软件授权")
        print("   • 请联系软件供应商，提供新的机器码获取授权")
        print("   • 如有问题，可以使用备份文件恢复")
        
        return True

def main():
    """主函数"""
    migrator = MachineCodeMigrator()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--compare":
            # 仅比较机器码
            migrator.compare_machine_codes()
        elif sys.argv[1] == "--force":
            # 强制迁移
            migrator.migrate(force=True)
        elif sys.argv[1] == "--report":
            # 仅生成报告
            migrator.create_migration_report()
        else:
            print("用法:")
            print("  python migrate_machine_code.py           # 自动迁移")
            print("  python migrate_machine_code.py --compare # 仅比较机器码")
            print("  python migrate_machine_code.py --force   # 强制迁移")
            print("  python migrate_machine_code.py --report  # 生成迁移报告")
    else:
        # 交互式迁移
        print("机器码迁移工具")
        print("=" * 40)
        
        response = input("是否要检查机器码变化并进行迁移？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            migrator.migrate()
        else:
            print("迁移已取消")

if __name__ == "__main__":
    main()
