#!/usr/bin/env python3
"""
机器码生成性能测试工具

用于测试优化后的机器码生成速度
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from encryption.machine_code import MachineCodeGenerator

def test_performance():
    """测试机器码生成性能"""
    print("=" * 80)
    print("机器码生成性能测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    generator = MachineCodeGenerator()
    
    # 1. 测试无缓存性能（首次生成）
    print("1. 首次生成测试（无缓存）...")
    generator.clear_cache()
    
    start_time = time.time()
    machine_code = generator.get_machine_code(use_cache=False)
    end_time = time.time()
    
    first_time = end_time - start_time
    print(f"   机器码: {machine_code}")
    print(f"   耗时: {first_time:.2f} 秒")
    
    if first_time > 10:
        print("   ⚠️  首次生成时间较长，可能需要进一步优化")
    elif first_time > 5:
        print("   ⚠️  首次生成时间适中")
    else:
        print("   ✅ 首次生成速度良好")
    
    print()
    
    # 2. 测试缓存性能
    print("2. 缓存性能测试...")
    
    start_time = time.time()
    cached_code = generator.get_machine_code(use_cache=True)
    end_time = time.time()
    
    cache_time = end_time - start_time
    print(f"   机器码: {cached_code}")
    print(f"   耗时: {cache_time:.4f} 秒")
    
    if cached_code == machine_code:
        print("   ✅ 缓存机器码与原始机器码一致")
    else:
        print("   ❌ 缓存机器码与原始机器码不一致")
    
    if cache_time < 0.1:
        print("   ✅ 缓存读取速度优秀")
    else:
        print("   ⚠️  缓存读取速度需要优化")
    
    print()
    
    # 3. 测试多次生成的稳定性和性能
    print("3. 多次生成稳定性测试...")
    
    times = []
    codes = []
    
    for i in range(3):
        start_time = time.time()
        code = generator.get_machine_code(use_cache=True)
        end_time = time.time()
        
        times.append(end_time - start_time)
        codes.append(code)
        print(f"   第 {i+1} 次: {code} (耗时: {times[-1]:.4f}秒)")
    
    # 检查稳定性
    unique_codes = set(codes)
    if len(unique_codes) == 1:
        print("   ✅ 多次生成结果稳定")
    else:
        print("   ❌ 多次生成结果不稳定")
    
    # 检查性能
    avg_time = sum(times) / len(times)
    if avg_time < 0.1:
        print(f"   ✅ 平均生成时间优秀: {avg_time:.4f}秒")
    else:
        print(f"   ⚠️  平均生成时间: {avg_time:.4f}秒")
    
    print()
    
    # 4. 硬件组件获取详细测试
    print("4. 硬件组件获取详细测试...")
    
    start_time = time.time()
    components = generator.get_hardware_components(use_cache=False, timeout=10)
    end_time = time.time()
    
    component_time = end_time - start_time
    print(f"   硬件组件获取耗时: {component_time:.2f}秒")
    
    print("   硬件组件详情:")
    for key, value in components.items():
        if key != 'timestamp':
            print(f"     {key}: {value}")
    
    if component_time < 5:
        print("   ✅ 硬件组件获取速度良好")
    elif component_time < 10:
        print("   ⚠️  硬件组件获取速度适中")
    else:
        print("   ❌ 硬件组件获取速度较慢")
    
    print()
    
    # 5. 总结报告
    print("=" * 80)
    print("性能测试总结")
    print("=" * 80)
    
    print(f"📊 性能指标:")
    print(f"   • 首次生成时间: {first_time:.2f}秒")
    print(f"   • 缓存读取时间: {cache_time:.4f}秒")
    print(f"   • 硬件获取时间: {component_time:.2f}秒")
    print(f"   • 平均生成时间: {avg_time:.4f}秒")
    
    print(f"\n🎯 优化效果:")
    if first_time < 5:
        print("   ✅ 首次生成速度已优化")
    else:
        print("   ⚠️  首次生成仍需优化")
    
    if cache_time < 0.1:
        print("   ✅ 缓存机制高效")
    else:
        print("   ⚠️  缓存机制需要改进")
    
    if len(unique_codes) == 1:
        print("   ✅ 生成结果稳定")
    else:
        print("   ❌ 生成结果不稳定")
    
    print(f"\n💡 建议:")
    if first_time > 10:
        print("   • 考虑增加更多快速获取方法")
        print("   • 减少WMI查询的使用")
    elif first_time > 5:
        print("   • 可以进一步优化超时设置")
    
    if component_time > 8:
        print("   • 建议用户首次运行后重启软件以使用缓存")
    
    print(f"\n📁 缓存文件位置: {generator._cache_file}")

def main():
    """主函数"""
    try:
        test_performance()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
