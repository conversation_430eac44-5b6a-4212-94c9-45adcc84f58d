#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
邮件发送核心模块
负责处理邮件发送的具体实现
"""

import smtplib
import ssl
import os
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from email import encoders
from email.header import Header
from datetime import datetime
import threading
import time
from core.chart_generator import ViolationChartGenerator
from core.data_exporter import ViolationDataExporter
from typing import Callable, Optional


class EmailSender:
    """邮件发送类"""
    
    def __init__(self, config):
        """初始化邮件发送器
        
        Args:
            config (dict): 邮件配置信息
        """
        self.config = config
        self.is_sending = False
        self.send_thread = None
        self.status_callback = None
        self.chart_generator = ViolationChartGenerator()
        self.data_exporter = ViolationDataExporter()
        self.temp_files = []  # 临时文件列表，用于清理
        self._lock = threading.Lock()
    
    def set_status_callback(self, callback):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def _update_status(self, message):
        """更新状态"""
        if self.status_callback:
            self.status_callback(message)
        print(f"EmailSender: {message}")
    
    def test_connection(self):
        """测试邮箱连接"""
        try:
            self._update_status("正在测试邮箱连接...")
            
            # 创建SMTP连接
            if self.config.get("use_ssl", False):
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(
                    self.config["smtp_server"], 
                    self.config["smtp_port"], 
                    context=context
                )
            else:
                server = smtplib.SMTP(
                    self.config["smtp_server"], 
                    self.config["smtp_port"]
                )
                if self.config.get("use_tls", True):
                    server.starttls()
            
            # 登录
            server.login(self.config["username"], self.config["password"])
            server.quit()
            
            self._update_status("邮箱连接测试成功！")
            return True, "连接测试成功"
            
        except smtplib.SMTPAuthenticationError:
            error_msg = "邮箱认证失败，请检查用户名和密码"
            self._update_status(f"连接测试失败: {error_msg}")
            return False, error_msg
        except smtplib.SMTPConnectError:
            error_msg = "无法连接到SMTP服务器，请检查服务器地址和端口"
            self._update_status(f"连接测试失败: {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"连接测试失败: {str(e)}"
            self._update_status(error_msg)
            return False, error_msg
    
    def send_test_email(self, recipient):
        """发送测试邮件"""
        try:
            self._update_status(f"正在发送测试邮件到 {recipient}...")
            
            # 创建邮件
            from email.utils import formataddr
            from email.header import Header
            
            msg = MIMEMultipart()
            from_name = self.config.get('from_name', '违章查询系统')
            from_addr = self.config['username']
            msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr))
            msg['To'] = recipient
            msg['Subject'] = Header("违章查询系统 - 邮箱配置测试", 'utf-8')
            
            # 邮件内容
            body = f"""
            <html>
            <body>
                <h2>邮箱配置测试</h2>
                <p>恭喜！您的邮箱配置已成功设置。</p>
                <p>发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>发送邮箱: {self.config['username']}</p>
                <hr>
                <p style="color: #666; font-size: 12px;">此邮件由违章查询系统自动发送，请勿回复。</p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # 发送邮件
            success, error_msg = self._send_email(msg, [recipient])
            
            if success:
                self._update_status("测试邮件发送成功！")
                return True, "测试邮件发送成功"
            else:
                return False, error_msg
                
        except Exception as e:
            error_msg = f"发送测试邮件失败: {str(e)}"
            self._update_status(error_msg)
            return False, error_msg
    
    def send_enhanced_violation_email(self, email_data, callback=None):
        """发送增强的违章记录邮件

        Args:
            email_data: 邮件数据字典，包含：
                - recipients: 收件人列表
                - subject: 邮件主题
                - content: 邮件正文
                - plate: 车牌号
                - violations_data: 违章数据
                - attachments: 附件列表
                - options: 选项配置
            callback: 发送完成回调函数
        """
        if self.is_sending:
            return False, "正在发送邮件，请等待完成"

        self.is_sending = True
        self.send_thread = threading.Thread(
            target=self._send_enhanced_email_thread,
            args=(email_data, callback)
        )
        self.send_thread.daemon = True
        self.send_thread.start()

        return True, "开始发送邮件"

    def send_violation_email(self, recipient, plate, violations_data, callback=None):
        """发送违章记录邮件（兼容旧接口）

        Args:
            recipient: 收件人邮箱
            plate: 车牌号
            violations_data: 违章数据字典，包含violations列表和统计信息
            callback: 发送完成回调函数
        """
        # 构建邮件数据
        email_data = {
            'recipients': [recipient],
            'subject': f"车辆违章记录通知 - {plate}",
            'content': f"车牌 {plate} 的违章记录详情，请查看邮件内容。",
            'plate': plate,
            'violations_data': violations_data,
            'attachments': [],
            'options': {
                'include_summary': True,
                'include_chart': False,
                'include_trend': False,
                'attach_excel': False,
                'attach_csv': False,
                'attach_json': False
            }
        }

        return self.send_enhanced_violation_email(email_data, callback)

    def _send_enhanced_email_thread(self, email_data, callback):
        """增强邮件发送线程"""
        try:
            recipients = email_data['recipients']
            subject = email_data['subject']
            content = email_data['content']
            plate = email_data['plate']
            violations_data = email_data['violations_data']
            attachments = email_data['attachments']
            options = email_data['options']

            self._update_status(f"正在为车牌 {plate} 准备邮件内容...")

            # 生成附件
            generated_attachments = []

            # 生成数据文件附件
            if options.get('attach_excel'):
                excel_path = self.data_exporter.export_to_excel(violations_data, plate)
                if excel_path:
                    generated_attachments.append(excel_path)
                    self.temp_files.append(excel_path)
                    self._update_status("已生成Excel附件")

            if options.get('attach_csv'):
                csv_path = self.data_exporter.export_to_csv(violations_data, plate)
                if csv_path:
                    generated_attachments.append(csv_path)
                    self.temp_files.append(csv_path)
                    self._update_status("已生成CSV附件")

            if options.get('attach_json'):
                json_path = self.data_exporter.export_to_json(violations_data, plate)
                if json_path:
                    generated_attachments.append(json_path)
                    self.temp_files.append(json_path)
                    self._update_status("已生成JSON附件")

            # 生成图表
            chart_base64 = None
            trend_chart_base64 = None

            if options.get('include_chart'):
                chart_path, chart_base64 = self.chart_generator.generate_violation_summary_chart(violations_data, plate)
                if chart_path:
                    self.temp_files.append(chart_path)
                    self._update_status("已生成统计图表")

            if options.get('include_trend'):
                trend_path, trend_chart_base64 = self.chart_generator.generate_monthly_trend_chart(violations_data, plate)
                if trend_path:
                    self.temp_files.append(trend_path)
                    self._update_status("已生成趋势图表")

            # 生成HTML邮件内容
            html_content = self._generate_html_content(
                content, plate, violations_data, options,
                chart_base64, trend_chart_base64
            )

            # 合并所有附件
            all_attachments = attachments + generated_attachments

            # 发送给所有收件人
            success_count = 0
            for recipient in recipients:
                try:
                    self._update_status(f"正在发送邮件到 {recipient}...")

                    # 创建邮件
                    msg = self._create_email_message(recipient, subject, html_content, all_attachments)

                    # 发送邮件
                    success, error_msg = self._send_email(msg, [recipient])

                    if success:
                        success_count += 1
                        self._update_status(f"成功发送到 {recipient}")
                    else:
                        self._update_status(f"发送到 {recipient} 失败: {error_msg}")

                    # 添加延迟避免发送过快
                    time.sleep(1)

                except Exception as e:
                    self._update_status(f"发送到 {recipient} 时发生错误: {str(e)}")

            # 清理临时文件
            self._cleanup_temp_files()

            # 显示最终结果
            self._update_status(f"发送完成！成功: {success_count}/{len(recipients)}")
            result = (True, f"邮件发送完成！成功: {success_count}/{len(recipients)}")

        except Exception as e:
            error_msg = f"发送邮件失败: {str(e)}"
            self._update_status(error_msg)
            result = (False, error_msg)

            # 清理临时文件
            self._cleanup_temp_files()

        finally:
            self.is_sending = False
            if callback:
                callback(result[0], result[1])

    def _generate_html_content(self, content, plate, violations_data, options, chart_base64=None, trend_chart_base64=None):
        """生成HTML邮件内容"""
        violations = violations_data.get('violations', [])

        # 基础HTML结构
        html = f"""
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff; }}
                .content {{ margin-bottom: 20px; white-space: pre-line; }}
                .violation-table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                .violation-table th, .violation-table td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                .violation-table th {{ background-color: #007bff; color: white; font-weight: bold; }}
                .violation-table tr:nth-child(even) {{ background-color: #f8f9fa; }}
                .summary {{ background-color: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .chart-container {{ text-align: center; margin: 20px 0; }}
                .chart-container img {{ max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; }}
                .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                .status-processed {{ color: #28a745; font-weight: bold; }}
                .status-unpaid {{ color: #ffc107; font-weight: bold; }}
                .status-unprocessed {{ color: #dc3545; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚗 车辆违章记录通知</h2>
                <p><strong>车牌号：</strong>{plate}</p>
                <p><strong>查询时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <div class="content">
                {content}
            </div>
        """

        # 添加违章汇总表格
        if options.get('include_summary') and violations:
            html += """
            <div class="summary">
                <h3>📊 违章汇总信息</h3>
                <p><strong>总违章数：</strong>{} 条</p>
                <p><strong>总罚款金额：</strong>¥{}</p>
                <p><strong>总记分：</strong>{} 分</p>
            </div>
            """.format(
                violations_data.get('total_count', len(violations)),
                violations_data.get('total_fine', 0),
                violations_data.get('total_points', 0)
            )

            # 添加详细违章表格
            html += """
            <h3>📋 详细违章记录</h3>
            <table class="violation-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>违法时间</th>
                        <th>违法地点</th>
                        <th>违法描述</th>
                        <th>罚款金额</th>
                        <th>记分</th>
                        <th>处理状态</th>
                    </tr>
                </thead>
                <tbody>
            """

            for idx, violation in enumerate(violations, 1):
                # 处理状态
                clbj = violation.get('clbj', '')
                jkbj = violation.get('jkbj', '')
                if clbj == "1" and jkbj == "1":
                    status = '<span class="status-processed">已处理已缴费</span>'
                elif clbj == "1":
                    status = '<span class="status-unpaid">已处理未缴费</span>'
                else:
                    status = '<span class="status-unprocessed">未处理</span>'

                html += f"""
                <tr>
                    <td>{idx}</td>
                    <td>{violation.get('wfsj', '')}</td>
                    <td>{violation.get('wfdz', '')}</td>
                    <td>{violation.get('wfms', '')}</td>
                    <td>¥{violation.get('fkje', '0')}</td>
                    <td>{violation.get('_points', '0')}分</td>
                    <td>{status}</td>
                </tr>
                """

            html += """
                </tbody>
            </table>
            """

        # 添加统计图表
        if chart_base64:
            html += f"""
            <div class="chart-container">
                <h3>📈 违章统计图表</h3>
                <img src="data:image/png;base64,{chart_base64}" alt="违章统计图表" />
            </div>
            """

        # 添加趋势图表
        if trend_chart_base64:
            html += f"""
            <div class="chart-container">
                <h3>📊 月度违章趋势</h3>
                <img src="data:image/png;base64,{trend_chart_base64}" alt="月度违章趋势" />
            </div>
            """

        # 添加页脚
        html += """
            <div class="footer">
                <p>📧 此邮件由违章查询系统自动发送，请勿直接回复。</p>
                <p>🔒 如有疑问，请联系系统管理员。</p>
                <p>⏰ 发送时间：{}</p>
            </div>
        </body>
        </html>
        """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        return html

    def _create_email_message(self, recipient, subject, html_content, attachments):
        """创建邮件消息"""
        from email.utils import formataddr
        from email.header import Header
        
        msg = MIMEMultipart()
        # 使用formataddr正确格式化From地址
        from_name = self.config.get('from_name', '违章查询系统')
        from_addr = self.config['username']
        msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr))
        msg['To'] = recipient
        msg['Subject'] = Header(subject, 'utf-8')

        # 添加HTML正文
        msg.attach(MIMEText(html_content, 'html', 'utf-8'))

        # 添加附件
        for attachment_path in attachments:
            if os.path.exists(attachment_path):
                try:
                    with open(attachment_path, 'rb') as f:
                        attachment_data = f.read()

                    filename = os.path.basename(attachment_path)

                    # 根据文件类型选择MIME类型
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                        attachment = MIMEImage(attachment_data)
                        attachment.add_header('Content-Disposition', f'attachment; filename="{filename}"')
                    elif filename.lower().endswith(('.xlsx', '.xls')):
                        # Excel文件
                        attachment = MIMEBase('application', 'vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                        attachment.set_payload(attachment_data)
                        encoders.encode_base64(attachment)
                        # 使用RFC2231编码文件名以支持中文
                        from email.utils import encode_rfc2231
                        encoded_filename = encode_rfc2231(filename, charset='utf-8')
                        attachment.add_header('Content-Disposition', f'attachment; filename*={encoded_filename}')
                    elif filename.lower().endswith('.csv'):
                        # CSV文件
                        attachment = MIMEBase('text', 'csv')
                        attachment.set_payload(attachment_data)
                        encoders.encode_base64(attachment)
                        from email.utils import encode_rfc2231
                        encoded_filename = encode_rfc2231(filename, charset='utf-8')
                        attachment.add_header('Content-Disposition', f'attachment; filename*={encoded_filename}')
                    else:
                        # 其他文件类型
                        attachment = MIMEBase('application', 'octet-stream')
                        attachment.set_payload(attachment_data)
                        encoders.encode_base64(attachment)
                        from email.utils import encode_rfc2231
                        encoded_filename = encode_rfc2231(filename, charset='utf-8')
                        attachment.add_header('Content-Disposition', f'attachment; filename*={encoded_filename}')

                    msg.attach(attachment)

                except Exception as e:
                    self._update_status(f"添加附件 {filename} 失败: {e}")

        return msg

    def _cleanup_temp_files(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"清理临时文件失败: {e}")

        self.temp_files.clear()
    
    def _send_violation_email_thread(self, recipient, plate, violations_data, callback):
        """违章邮件发送线程"""
        try:
            self._update_status(f"正在为车牌 {plate} 生成邮件内容...")
            
            # 生成违章记录表格行
            violation_rows = ""
            violations = violations_data.get('violations', [])
            
            for violation in violations:
                # 处理状态显示
                process_status = self._get_process_status_text(
                    violation.get('clbj', ''), 
                    violation.get('jkbj', '')
                )
                
                violation_rows += f"""
                <tr>
                    <td>{violation.get('wfsj', '')}</td>
                    <td>{violation.get('wfdz', '')}</td>
                    <td>{violation.get('wfms', '')}</td>
                    <td>¥{violation.get('fkje', '0')}</td>
                    <td>{violation.get('_points', '0')}分</td>
                    <td>{process_status}</td>
                </tr>
                """
            
            # 使用模板生成邮件内容
            template = self.config.get('email_templates', {})
            subject_template = template.get('subject', '车辆违章记录通知 - {plate}')
            body_template = template.get('body', '')
            
            # 替换模板变量
            subject = subject_template.format(plate=plate)
            body = body_template.format(
                plate=plate,
                query_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                violation_rows=violation_rows,
                total_count=violations_data.get('total_count', len(violations)),
                total_fine=violations_data.get('total_fine', 0),
                total_points=violations_data.get('total_points', 0)
            )
            
            # 创建邮件
            from email.utils import formataddr
            from email.header import Header
            
            msg = MIMEMultipart()
            from_name = self.config.get('from_name', '违章查询系统')
            from_addr = self.config['username']
            msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr))
            msg['To'] = recipient
            msg['Subject'] = Header(subject, 'utf-8')
            
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # 发送邮件
            self._update_status(f"正在发送邮件到 {recipient}...")
            success, error_msg = self._send_email(msg, [recipient])
            
            if success:
                self._update_status(f"违章记录邮件发送成功！收件人: {recipient}")
                result = (True, "邮件发送成功")
            else:
                result = (False, error_msg)
            
        except Exception as e:
            error_msg = f"发送违章邮件失败: {str(e)}"
            self._update_status(error_msg)
            result = (False, error_msg)
        
        finally:
            self.is_sending = False
            if callback:
                callback(result[0], result[1])
    
    def _send_email(self, msg, recipients):
        """发送邮件的底层方法"""
        try:
            with self._lock:
                # 创建SMTP连接
                if self.config.get("use_ssl", False):
                    context = ssl.create_default_context()
                    server = smtplib.SMTP_SSL(
                        self.config["smtp_server"], 
                        self.config["smtp_port"], 
                        context=context
                    )
                else:
                    server = smtplib.SMTP(
                        self.config["smtp_server"], 
                        self.config["smtp_port"]
                    )
                    if self.config.get("use_tls", True):
                        server.starttls()
            
            # 登录并发送
            server.login(self.config["username"], self.config["password"])
            # 使用as_bytes()避免编码问题，或者使用as_string()的policy参数
            try:
                # 优先使用as_bytes()方法
                text = msg.as_bytes()
                server.sendmail(self.config["username"], recipients, text)
            except (AttributeError, UnicodeEncodeError):
                # 如果as_bytes()不可用或编码失败，使用as_string()并指定编码策略
                try:
                    from email.policy import default
                    text = msg.as_string(policy=default)
                    server.sendmail(self.config["username"], recipients, text.encode('utf-8'))
                except Exception:
                    # 最后的备用方案：使用传统方法但确保UTF-8编码
                    import sys
                    if sys.version_info >= (3, 3):
                        from email.policy import SMTP
                        text = msg.as_string(policy=SMTP)
                    else:
                        text = msg.as_string()
                    server.sendmail(self.config["username"], recipients, text.encode('utf-8'))
            server.quit()
            
            return True, "发送成功"
            
        except smtplib.SMTPAuthenticationError as e:
            return False, f"SMTP认证失败: {str(e)}\n请检查用户名和密码（QQ邮箱需要使用授权码）"
        except smtplib.SMTPConnectError as e:
            return False, f"SMTP连接失败: {str(e)}\n请检查服务器地址和端口"
        except smtplib.SMTPServerDisconnected as e:
            return False, f"SMTP服务器断开连接: {str(e)}"
        except Exception as e:
            return False, f"发送失败: {str(e)}"
    
    def _get_process_status_text(self, clbj, jkbj):
        """获取处理状态文本"""
        if clbj == "1" and jkbj == "1":
            return "已处理已缴费"
        elif clbj == "1" and jkbj != "1":
            return "已处理未缴费"
        elif clbj != "1":
            return "未处理"
        else:
            return "状态未知"
    
    def stop_sending(self):
        """停止发送（如果正在发送）"""
        if self.is_sending and self.send_thread:
            self._update_status("正在停止邮件发送...")
            # 注意：这里只是设置标志，实际的线程可能需要一些时间才能停止
            self.is_sending = False

    def send_email(self, subject: str, content: str, recipients: list, 
                  callback: Optional[Callable[[bool, str], None]] = None):
        """发送邮件
        
        Args:
            subject (str): 邮件主题
            content (str): 邮件内容
            recipients (list): 收件人列表
            callback (callable): 发送完成后的回调函数
        """
        def _send():
            try:
                from email.utils import formataddr
                from email.header import Header

                msg = MIMEMultipart()
                # 使用formataddr正确格式化From地址以避免编码问题
                from_name = self.config.get('from_name', '违章查询系统')
                from_addr = self.config['username']
                msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr))
                msg['To'] = "; ".join(recipients)
                msg['Subject'] = Header(subject, 'utf-8')
                
                msg.attach(MIMEText(content, 'html', 'utf-8'))
                
                with self._lock:
                    server = smtplib.SMTP(self.config['smtp_server'], int(self.config['smtp_port']))
                    if self.config['use_tls']:
                        server.starttls()
                    server.login(self.config['username'], self.config['password'])
                    # 使用as_bytes()避免编码问题
                    try:
                        text = msg.as_bytes()
                        server.sendmail(self.config['username'], recipients, text)
                    except (AttributeError, UnicodeEncodeError):
                        # 如果as_bytes()不可用或编码失败，使用as_string()并指定编码策略
                        try:
                            from email.policy import default
                            text = msg.as_string(policy=default)
                            server.sendmail(self.config['username'], recipients, text.encode('utf-8'))
                        except Exception:
                            # 最后的备用方案
                            import sys
                            if sys.version_info >= (3, 3):
                                from email.policy import SMTP
                                text = msg.as_string(policy=SMTP)
                            else:
                                text = msg.as_string()
                            server.sendmail(self.config['username'], recipients, text.encode('utf-8'))
                    server.quit()
                
                if callback:
                    callback(True, "邮件发送成功")
            except Exception as e:
                if callback:
                    callback(False, f"邮件发送失败: {str(e)}")
        
        # 在新线程中发送邮件
        thread = threading.Thread(target=_send)
        thread.daemon = True
        thread.start()