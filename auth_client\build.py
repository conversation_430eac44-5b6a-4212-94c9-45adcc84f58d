import subprocess
import os
import sys

def get_correct_path_separator_for_pyinstaller_add_data():
    """
    PyInstaller's --add-data argument expects ':' as a separator
    between source and destination, regardless of OS.
    However, for paths *within* the source or destination part,
    OS-specific separators are needed if they are part of a longer path.
    In this specific case, "auth_history.db:." means "auth_history.db" from
    current dir maps to "." in the bundle.
    """
    return ":" # PyInstaller specifically uses ':'

if __name__ == "__main__":
    print("======================================================================")
    print(" Python Application Packager (using PyInstaller)")
    print("======================================================================")
    print("")
    print(" IMPORTANT: Ensure PyInstaller is installed.")
    print(" If not, open a new terminal and run: pip install pyinstaller")
    print("")
    print(" This script will attempt to package 'run.py' into 'AuthClient.exe'.")
    print(" The output will be in a 'dist' folder.")
    print("")
    print(" Starting PyInstaller...")
    print("======================================================================")
    print("")

    # Get the directory of the build script
    base_path = os.path.dirname(os.path.abspath(__file__))

    # Construct paths relative to the script's location
    icon_path = os.path.join(base_path, "icon.ico")
    data_file_path = os.path.join(base_path, "auth_history.db")
    main_script_path = os.path.join(base_path, "run.py")

    # Ensure paths are correct, especially for --add-data
    # PyInstaller's --add-data uses ':' as a separator for source:destination
    # The source path itself should use os.sep if it's more complex,
    # but for a simple filename like "auth_history.db", it's straightforward.
    add_data_arg = f"{data_file_path}{get_correct_path_separator_for_pyinstaller_add_data()}."

    command = [
        'pyinstaller',
        '--name', 'AuthClient',
        '--onefile',
        '--windowed',
        '--icon', icon_path,
        '--add-data', add_data_arg,
        main_script_path
    ]

    print(f"Executing command: {' '.join(command)}")
    print("")

    try:
        # For Windows, shell=True might be needed if pyinstaller is not directly in PATH
        # but it's generally safer to ensure pyinstaller's directory is in PATH.
        # If using a virtual environment, pyinstaller should be in the Scripts folder.
        # We will try without shell=True first for better security and cross-platform behavior.
        process = subprocess.run(command, capture_output=True, text=True, check=False, cwd=base_path)

        if process.stdout:
            print("------- PyInstaller STDOUT -------")
            print(process.stdout)
            print("----------------------------------")
        if process.stderr:
            print("------- PyInstaller STDERR -------")
            print(process.stderr)
            print("----------------------------------")

        if process.returncode == 0:
            print("")
            print("======================================================================")
            print(" Packaging process finished successfully.")
            print(f" Check the '{os.path.join(base_path, 'dist')}' folder for 'AuthClient.exe'.")
            print("======================================================================")
        else:
            print("")
            print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
            print(f" Packaging process FAILED with return code: {process.returncode}")
            print(" Please review the output above for errors.")
            print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")

    except FileNotFoundError:
        print("")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print(" ERROR: PyInstaller command not found.")
        print(" Please ensure PyInstaller is installed and in your system's PATH.")
        print(" Try: pip install pyinstaller")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    except Exception as e:
        print("")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print(f" An unexpected error occurred: {e}")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")

    print("")
    # In Python, there's no direct equivalent to 'pause' that works universally
    # and keeps the window open after script execution if run by double-clicking.
    # If running from a terminal, the terminal will remain open.
    # For a "pause" like effect if double-clicked, one might need a GUI prompt or input().
    input("Press Enter to exit...") 